"""
System serializers for 快速部署测试
Based on the existing system management functionality
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.validators import validate_email
from .models import SystemSetting, EmailTemplate, SystemNotification, NotificationRecipient
import json

User = get_user_model()


class SystemSettingSerializer(serializers.ModelSerializer):
    """System setting serializer"""

    modified_by_name = serializers.SerializerMethodField()

    class Meta:
        model = SystemSetting
        fields = [
            "id",
            "key",
            "name",
            "value",
            "description",
            "category",
            "value_type",
            "is_public",
            "is_readonly",
            "requires_restart",
            "order",
            "group",
            "modified_by",
            "modified_by_name",
            "created_at",
        ]
        read_only_fields = ["id", "modified_by", "created_at"]

    def get_modified_by_name(self, obj):
        """Get modifier name"""
        if obj.modified_by:
            return obj.modified_by.get_full_name() or obj.modified_by.username
        return None

    def validate_value(self, value):
        """Validate setting value based on key and validation rules"""
        key = self.initial_data.get("key") or (
            self.instance.key if self.instance else None
        )

        # Add custom validation based on your application's needs
        # Example validations:
        
        if key == "session_timeout_minutes":
            try:
                minutes = int(value)
                if minutes < 5 or minutes > 1440:  # 5 minutes to 24 hours
                    raise serializers.ValidationError(
                        "Session timeout must be between 5-1440 minutes"
                    )
            except ValueError:
                raise serializers.ValidationError("Session timeout must be a number")

        elif key == "max_login_attempts":
            try:
                attempts = int(value)
                if attempts < 3 or attempts > 10:
                    raise serializers.ValidationError("Max login attempts must be between 3-10")
            except ValueError:
                raise serializers.ValidationError("Max login attempts must be a number")

        elif key in ["smtp_host", "smtp_username"]:
            if not value.strip():
                raise serializers.ValidationError("This setting cannot be empty")

        elif key == "smtp_port":
            try:
                port = int(value)
                if port < 1 or port > 65535:
                    raise serializers.ValidationError("SMTP port must be between 1-65535")
            except ValueError:
                raise serializers.ValidationError("SMTP port must be a number")

        elif key == "smtp_from_email":
            if value:
                validate_email(value)

        return value

    def update(self, instance, validated_data):
        """Update system setting with user tracking"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["modified_by"] = request.user
        return super().update(instance, validated_data)


class SystemSettingUpdateSerializer(serializers.ModelSerializer):
    """System setting update serializer (simplified for bulk updates)"""

    class Meta:
        model = SystemSetting
        fields = ["key", "value"]

    def validate(self, attrs):
        """Validate setting update"""
        key = attrs.get("key")
        value = attrs.get("value")

        try:
            setting = SystemSetting.objects.get(key=key)
            if setting.is_readonly:
                raise serializers.ValidationError(f"Setting '{key}' is read-only")
            
            # Validate value using the setting's validation rules
            is_valid, error_message = setting.validate_value(value)
            if not is_valid:
                raise serializers.ValidationError(f"Invalid value for '{key}': {error_message}")
                
        except SystemSetting.DoesNotExist:
            raise serializers.ValidationError(f"Setting '{key}' does not exist")

        return attrs


class EmailTemplateSerializer(serializers.ModelSerializer):
    """Email template serializer"""

    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)

    class Meta:
        model = EmailTemplate
        fields = [
            "id",
            "name",
            "template_type",
            "subject",
            "content",
            "is_active",
            "is_default",
            "created_by",
            "created_by_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_by", "created_at", "updated_at"]

    def validate(self, attrs):
        """Validate email template"""
        template_type = attrs.get("template_type")
        is_default = attrs.get("is_default", False)

        # Check if another default template exists for this type
        if is_default:
            existing_default = EmailTemplate.objects.filter(
                template_type=template_type, is_default=True
            )
            if self.instance:
                existing_default = existing_default.exclude(id=self.instance.id)
            
            if existing_default.exists():
                raise serializers.ValidationError(
                    f"A default template already exists for type '{template_type}'"
                )

        return attrs

    def create(self, validated_data):
        """Create email template with user tracking"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user
        return super().create(validated_data)


class SystemNotificationSerializer(serializers.ModelSerializer):
    """System notification serializer"""

    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)
    recipient_count = serializers.SerializerMethodField()

    class Meta:
        model = SystemNotification
        fields = [
            "id",
            "title",
            "message",
            "notification_type",
            "priority",
            "is_global",
            "is_persistent",
            "auto_dismiss",
            "dismiss_after",
            "action_url",
            "action_text",
            "created_by",
            "created_by_name",
            "recipient_count",
            "created_at",
            "expires_at",
        ]
        read_only_fields = ["id", "created_by", "created_at"]

    def get_recipient_count(self, obj):
        """Get number of recipients"""
        return obj.target_users.count()

    def create(self, validated_data):
        """Create notification with user tracking"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user
        return super().create(validated_data)


class NotificationRecipientSerializer(serializers.ModelSerializer):
    """Notification recipient serializer"""

    notification_title = serializers.CharField(source="notification.title", read_only=True)
    notification_type = serializers.CharField(source="notification.notification_type", read_only=True)
    notification_priority = serializers.CharField(source="notification.priority", read_only=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)

    class Meta:
        model = NotificationRecipient
        fields = [
            "id",
            "notification",
            "notification_title",
            "notification_type",
            "notification_priority",
            "user",
            "user_name",
            "is_read",
            "is_dismissed",
            "read_at",
            "dismissed_at",
        ]
        read_only_fields = ["id", "read_at", "dismissed_at"]


class UserNotificationSerializer(serializers.ModelSerializer):
    """User notification serializer for current user's notifications"""

    notification = SystemNotificationSerializer(read_only=True)

    class Meta:
        model = NotificationRecipient
        fields = [
            "id",
            "notification",
            "is_read",
            "is_dismissed",
            "read_at",
            "dismissed_at",
        ]
        read_only_fields = ["id", "notification", "read_at", "dismissed_at"]


class SystemStatsSerializer(serializers.Serializer):
    """System statistics serializer"""

    total_users = serializers.IntegerField(read_only=True)
    active_users = serializers.IntegerField(read_only=True)
    total_settings = serializers.IntegerField(read_only=True)
    total_notifications = serializers.IntegerField(read_only=True)
    unread_notifications = serializers.IntegerField(read_only=True)

    # System health
    system_status = serializers.CharField(read_only=True)
    database_status = serializers.CharField(read_only=True)
    cache_status = serializers.CharField(read_only=True)

    # Resource usage
    memory_usage = serializers.DictField(read_only=True)
    disk_usage = serializers.DictField(read_only=True)

    # Recent activity
    recent_logins = serializers.ListField(read_only=True)
    recent_settings_changes = serializers.ListField(read_only=True)


class SystemHealthSerializer(serializers.Serializer):
    """System health check serializer"""

    status = serializers.CharField(read_only=True)
    timestamp = serializers.DateTimeField(read_only=True)
    
    # Component status
    database = serializers.DictField(read_only=True)
    cache = serializers.DictField(read_only=True)
    storage = serializers.DictField(read_only=True)
    
    # Performance metrics
    response_time = serializers.FloatField(read_only=True)
    memory_usage_percent = serializers.FloatField(read_only=True)
    cpu_usage_percent = serializers.FloatField(read_only=True)
    disk_usage_percent = serializers.FloatField(read_only=True)


class BulkSettingsUpdateSerializer(serializers.Serializer):
    """Bulk settings update serializer"""

    settings = serializers.ListField(
        child=SystemSettingUpdateSerializer(),
        min_length=1,
        help_text="List of settings to update"
    )

    def validate_settings(self, value):
        """Validate settings list"""
        keys = [setting["key"] for setting in value]
        
        # Check for duplicates
        if len(keys) != len(set(keys)):
            raise serializers.ValidationError("Duplicate setting keys found")
        
        # Validate each setting exists and is not readonly
        for setting_data in value:
            key = setting_data["key"]
            try:
                setting = SystemSetting.objects.get(key=key)
                if setting.is_readonly:
                    raise serializers.ValidationError(f"Setting '{key}' is read-only")
            except SystemSetting.DoesNotExist:
                raise serializers.ValidationError(f"Setting '{key}' does not exist")
        
        return value
