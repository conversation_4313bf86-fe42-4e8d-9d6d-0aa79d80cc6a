import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import { VitePWA } from 'vite-plugin-pwa'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'
  
  return {
    plugins: [
      vue(),
      vueJsx(),
      
      // Vue DevTools (development only)
      ...(isDevelopment && env.VITE_ENABLE_DEVTOOLS === 'true' ? [vueDevTools()] : []),
      
      // PWA (if enabled)
      ...(env.VITE_ENABLE_PWA === 'true' ? [
        VitePWA({
          registerType: 'autoUpdate',
          workbox: {
            globPatterns: ['**/*.{js,css,html,ico,png,svg}']
          },
          manifest: {
            name: env.VITE_APP_NAME || '快速部署测试',
            short_name: env.VITE_APP_TITLE || '快速部署测试',
            description: env.VITE_APP_DESCRIPTION || 'A Django + Vue.js application: 快速部署测试',
            theme_color: env.VITE_THEME_PRIMARY_COLOR || '#1890ff',
            icons: [
              {
                src: 'pwa-192x192.png',
                sizes: '192x192',
                type: 'image/png'
              },
              {
                src: 'pwa-512x512.png',
                sizes: '512x512',
                type: 'image/png'
              }
            ]
          }
        })
      ] : []),
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '#': resolve(__dirname, 'src'),
        '~': resolve(__dirname, 'src'),
      }
    },
    
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`
        }
      }
    },
    
    server: {
      host: env.VITE_DEV_SERVER_HOST || '0.0.0.0',
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 5173,
      open: env.VITE_DEV_SERVER_OPEN === 'true',
      https: env.VITE_ENABLE_HTTPS === 'true',
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
        }
      }
    },
    
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: env.VITE_BUILD_SOURCEMAP === 'true',
      minify: isProduction ? 'terser' : false,
      
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            antd: ['ant-design-vue', '@ant-design/icons-vue'],
            utils: ['axios', '@vueuse/core', 'dayjs', 'lodash-es']
          }
        }
      },
      
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction
        }
      }
    },
    
    define: {
      __APP_INFO__: JSON.stringify({
        name: env.VITE_APP_NAME || '快速部署测试',
        version: env.VITE_APP_VERSION || '1.0.0',
        buildTime: new Date().toISOString()
      })
    },
    
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'ant-design-vue',
        '@ant-design/icons-vue',
        'axios',
        '@vueuse/core',
        'dayjs'
      ]
    }
  }
})
