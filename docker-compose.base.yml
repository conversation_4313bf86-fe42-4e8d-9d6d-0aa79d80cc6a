version: '3.8'

# 使用预构建基础镜像的Docker Compose配置
# 适用于内网环境，无需重新构建镜像

services:
  # 数据库服务
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-{{PROJECT_SLUG}}_db}
      POSTGRES_USER: ${POSTGRES_USER:-{{PROJECT_SLUG}}_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-{{PROJECT_SLUG}}_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-{{PROJECT_SLUG}}_user} -d ${POSTGRES_DB:-{{PROJECT_SLUG}}_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis服务（用于缓存和会话）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 后端服务 - 使用预构建的基础镜像
  backend:
    image: locker-template-backend-base:latest
    volumes:
      - ./backend:/app
      - backend_media:/app/media
      - backend_static:/app/static
      - backend_logs:/app/logs
    ports:
      - "8001:8000"
    environment:
      - DEBUG=${DEBUG:-True}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-{{PROJECT_SLUG}}_user}:${POSTGRES_PASSWORD:-{{PROJECT_SLUG}}_password}@db:5432/${POSTGRES_DB:-{{PROJECT_SLUG}}_db}
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,backend}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:5173,http://127.0.0.1:5173}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 前端服务 - 使用预构建的基础镜像
  frontend:
    image: locker-template-frontend-base:latest
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8001}
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=5173
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  backend_media:
  backend_static:
  backend_logs:

networks:
  default:
    name: {{PROJECT_SLUG}}_network
