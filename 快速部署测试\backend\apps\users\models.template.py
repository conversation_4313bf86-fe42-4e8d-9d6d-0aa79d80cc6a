"""
User models for 快速部署测试
Based on the existing user management system
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class Department(models.Model):
    """Department model for organizational structure"""

    name = models.CharField(max_length=100, verbose_name=_("Department Name"))
    description = models.TextField(blank=True, verbose_name=_("Department Description"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Parent Department"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Department")
        verbose_name_plural = _("Departments")
        db_table = "快速部署测试_departments"

    def __str__(self):
        return self.name


class Team(models.Model):
    """Team model for project organization"""

    name = models.CharField(max_length=100, verbose_name=_("Team Name"))
    description = models.TextField(blank=True, verbose_name=_("Team Description"))
    department = models.ForeignKey(
        Department, on_delete=models.CASCADE, verbose_name=_("Department")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Team")
        verbose_name_plural = _("Teams")
        db_table = "快速部署测试_teams"

    def __str__(self):
        return self.name


class Role(models.Model):
    """Role model for permission management"""

    ROLE_CHOICES = [
        ("admin", _("System Administrator")),
        ("manager", _("Department Manager")),
        ("user", _("Regular User")),
        ("viewer", _("Read-only User")),
    ]

    name = models.CharField(
        max_length=50, choices=ROLE_CHOICES, verbose_name=_("Role Name")
    )
    description = models.TextField(blank=True, verbose_name=_("Role Description"))
    permissions = models.JSONField(default=list, verbose_name=_("Permission List"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Role")
        verbose_name_plural = _("Roles")
        db_table = "快速部署测试_roles"

    def __str__(self):
        return self.get_name_display()


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    Based on the existing user management system
    """

    email = models.EmailField(
        unique=True, blank=True, null=True, verbose_name=_("Email")
    )
    name = models.CharField(max_length=100, blank=True, verbose_name=_("Full Name"))
    phone = models.CharField(max_length=20, blank=True, verbose_name=_("Phone Number"))
    avatar = models.ImageField(
        upload_to="avatars/", blank=True, null=True, verbose_name=_("Avatar")
    )

    # Organizational relationships
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Department"),
    )
    team = models.ForeignKey(
        Team, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("Team")
    )
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("Role")
    )

    # User preferences
    home_path = models.CharField(
        max_length=255, default="/dashboard", verbose_name=_("Home Path")
    )

    # Security settings
    is_mfa_enabled = models.BooleanField(
        default=False, verbose_name=_("Enable Multi-Factor Authentication")
    )
    mfa_secret = models.CharField(
        max_length=32, blank=True, verbose_name=_("MFA Secret")
    )
    last_password_change = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Last Password Change")
    )

    # Account security
    failed_login_attempts = models.IntegerField(
        default=0, verbose_name=_("Failed Login Attempts")
    )
    locked_until = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Locked Until")
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    USERNAME_FIELD = "username"

    def save(self, *args, **kwargs):
        # Convert empty string to None to avoid unique constraint conflicts
        if self.email == "":
            self.email = None
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")
        db_table = "快速部署测试_users"

    def __str__(self):
        return f"{self.username} ({self.email})" if self.email else self.username

    @property
    def display_name(self):
        """Return the display name for the user."""
        return self.name or self.get_full_name() or self.username

    @property
    def is_locked(self):
        """Check if user account is locked"""
        if self.locked_until:
            from django.utils import timezone

            return timezone.now() < self.locked_until
        return False

    def reset_failed_attempts(self):
        """Reset failed login attempts"""
        self.failed_login_attempts = 0
        self.locked_until = None
        self.save(update_fields=["failed_login_attempts", "locked_until"])

    def increment_failed_attempts(self):
        """Increment failed login attempts and lock account if necessary"""
        self.failed_login_attempts += 1
        if (
            self.failed_login_attempts >= 5
        ):  # Lock account after 5 failed attempts for 30 minutes
            from django.utils import timezone
            from datetime import timedelta

            self.locked_until = timezone.now() + timedelta(minutes=30)
        self.save(update_fields=["failed_login_attempts", "locked_until"])
