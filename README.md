# {{PROJECT_NAME}}

基于Django + Vue.js的现代化Web应用项目模板。

## 🚀 快速开始

### 方式一：使用预构建基础镜像（推荐 - 内网友好）

使用预构建的基础镜像可以实现真正的快速部署，无需联网下载依赖。

#### 前置要求
- Docker
- Docker Compose
- 预构建的基础镜像

#### 构建基础镜像（一次性操作）

**Windows:**
```bash
scripts\build-images.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/build-images.sh
./scripts/build-images.sh
```

#### 快速部署

**Windows:**
```bash
scripts\quick-deploy.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/quick-deploy.sh
./scripts/quick-deploy.sh
```

### 方式二：Docker容器化部署（需要网络）

使用Docker构建镜像，需要联网下载依赖。

#### 启动项目

**Windows:**
```bash
scripts\docker-start.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/docker-start.sh
./scripts/docker-start.sh
```

**手动启动:**
```bash
# 构建并启动所有服务
docker-compose up -d

# 运行数据库迁移
docker-compose exec backend python manage.py migrate

# 初始化系统设置
docker-compose exec backend python manage.py init_system_settings

# 创建超级用户
docker-compose exec backend python manage.py createsuperuser
```

#### 停止项目

**Windows:**
```bash
scripts\docker-stop.bat
```

**Linux/macOS:**
```bash
./scripts/docker-stop.sh
```

### 方式三：本地开发环境

#### 后端设置

1. 创建虚拟环境：
```bash
cd backend
python -m venv venv
```

2. 激活虚拟环境：
```bash
# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 配置环境变量：
```bash
cp .env.example .env
# 编辑.env文件，配置数据库等设置
```

5. 运行数据库迁移：
```bash
python manage.py migrate
python manage.py init_system_settings
```

6. 创建超级用户：
```bash
python manage.py createsuperuser
```

7. 启动开发服务器：
```bash
python manage.py runserver 0.0.0.0:8001
```

#### 前端设置

1. 安装依赖：
```bash
cd frontend
npm install
```

2. 配置环境变量：
```bash
cp .env.example .env
# 编辑.env文件，配置API地址等设置
```

3. 启动开发服务器：
```bash
npm run dev
```

## 📋 访问地址

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8001
- **API文档**: http://localhost:8001/api/docs/
- **Django Admin**: http://localhost:8001/admin/

## 🔑 默认账户

- **用户名**: admin
- **密码**: admin123

## 🏗️ 项目结构

```
{{PROJECT_NAME}}/
├── backend/                 # Django后端
│   ├── apps/               # 应用模块
│   │   ├── users/          # 用户管理
│   │   ├── system/         # 系统管理
│   │   └── audit/          # 审计日志
│   ├── config/             # 项目配置
│   ├── requirements.txt    # Python依赖
│   └── Dockerfile         # 后端Docker配置
├── frontend/               # Vue.js前端
│   ├── src/               # 源代码
│   ├── package.json       # Node.js依赖
│   └── Dockerfile         # 前端Docker配置
├── scripts/               # 启动脚本
├── docker-compose.yml     # Docker编排配置
└── README.md             # 项目文档
```

## 🐳 Docker基础镜像

### 基础镜像优势
- ✅ **预安装依赖**: 所有Python和Node.js依赖都已预装
- ✅ **内网友好**: 无需联网下载依赖，支持离线部署
- ✅ **快速启动**: 跳过依赖安装步骤，启动速度快
- ✅ **版本一致**: 确保所有环境使用相同的依赖版本

### 镜像管理

```bash
# 查看构建的镜像
docker images | grep locker-template

# 导出镜像（用于离线部署）
docker save locker-template-backend-base:latest | gzip > backend-base.tar.gz
docker save locker-template-frontend-base:latest | gzip > frontend-base.tar.gz

# 导入镜像
docker load < backend-base.tar.gz
docker load < frontend-base.tar.gz

# 清理镜像
docker rmi locker-template-backend-base:latest locker-template-frontend-base:latest
```

详细信息请参考：[Docker基础镜像文档](docs/DOCKER_IMAGES.md)

## 🛠️ 开发工具

### Docker命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 进入容器
docker-compose exec backend bash
docker-compose exec frontend sh

# 停止并删除容器
docker-compose down

# 重新构建镜像
docker-compose build --no-cache
```

### 数据库管理

```bash
# 创建迁移文件
docker-compose exec backend python manage.py makemigrations

# 应用迁移
docker-compose exec backend python manage.py migrate

# 进入Django shell
docker-compose exec backend python manage.py shell

# 收集静态文件
docker-compose exec backend python manage.py collectstatic
```

## 🔧 配置说明

### 环境变量

**后端 (.env):**
- `DEBUG`: 调试模式
- `SECRET_KEY`: Django密钥
- `DATABASE_URL`: 数据库连接
- `REDIS_URL`: Redis连接
- `ALLOWED_HOSTS`: 允许的主机
- `CORS_ALLOWED_ORIGINS`: CORS允许的源

**前端 (.env):**
- `VITE_API_BASE_URL`: 后端API地址
- `VITE_DEV_SERVER_HOST`: 开发服务器主机
- `VITE_DEV_SERVER_PORT`: 开发服务器端口

### 数据库

默认使用PostgreSQL数据库，配置在docker-compose.yml中。
开发环境也可以使用SQLite。

### 缓存

使用Redis作为缓存和会话存储。

## 📚 技术栈

### 后端
- **Django 4.2+**: Web框架
- **Django REST Framework**: API框架
- **PostgreSQL**: 数据库
- **Redis**: 缓存和会话
- **JWT**: 身份认证

### 前端
- **Vue.js 3**: 前端框架
- **Vite**: 构建工具
- **Ant Design Vue**: UI组件库
- **Pinia**: 状态管理
- **Vue Router**: 路由管理

### 部署
- **Docker**: 容器化
- **Docker Compose**: 服务编排

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
