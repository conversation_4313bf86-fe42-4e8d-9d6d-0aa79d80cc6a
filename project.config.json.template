{"project": {"name": "{{PROJECT_NAME}}", "title": "{{PROJECT_TITLE}}", "description": "{{PROJECT_DESCRIPTION}}", "version": "1.0.0", "slug": "{{PROJECT_SLUG}}", "homepage": "{{PROJECT_HOMEPAGE}}", "repository": "{{PROJECT_REPOSITORY}}", "license": "MIT", "domain": "{{DOMAIN}}"}, "author": {"name": "{{AUTHOR_NAME}}", "email": "{{AUTHOR_EMAIL}}", "url": "{{AUTHOR_URL}}"}, "backend": {"framework": "Django", "version": "5.0+", "database": {"engine": "sqlite", "name": "{{PROJECT_SLUG}}.sqlite3"}, "host": "localhost", "port": 8000, "debug": true, "secret_key": "{{SECRET_KEY}}"}, "frontend": {"framework": "Vue.js", "version": "3.5+", "ui_library": "Ant Design Vue", "host": "localhost", "port": 5173, "api_base_url": "http://localhost:8000"}, "features": {"core": {"authentication": true, "authorization": true, "user_management": true, "api_documentation": true, "admin_interface": true}, "optional": {"email_notifications": false, "sms_notifications": false, "audit_logging": true, "rate_limiting": false, "caching": false, "background_tasks": false, "file_upload": true, "export_functionality": true, "internationalization": false, "pwa_support": false, "two_factor_auth": false}, "development": {"debug_toolbar": true, "cors": true, "dev_tools": true, "mock_data": false}}, "theme": {"primary_color": "#1890ff", "layout": "side", "dark_mode": false, "compact_mode": false}, "security": {"allowed_hosts": ["localhost", "127.0.0.1"], "cors_allowed_origins": ["http://localhost:5173", "http://localhost:3000"], "jwt_access_token_lifetime": 60, "jwt_refresh_token_lifetime": 7, "enable_https": false}, "database": {"sqlite": {"name": "{{PROJECT_SLUG}}.sqlite3"}, "mysql": {"host": "localhost", "port": 3306, "name": "{{PROJECT_SLUG}}_db", "user": "{{DB_USER}}", "password": "{{DB_PASSWORD}}"}, "postgresql": {"host": "localhost", "port": 5432, "name": "{{PROJECT_SLUG}}_db", "user": "{{DB_USER}}", "password": "{{DB_PASSWORD}}"}}, "email": {"backend": "django.core.mail.backends.console.EmailBackend", "host": "{{EMAIL_HOST}}", "port": 587, "use_tls": true, "user": "{{EMAIL_HOST_USER}}", "password": "{{EMAIL_HOST_PASSWORD}}", "from_email": "noreply@{{DOMAIN}}"}, "redis": {"url": "redis://localhost:6379/1", "cache_ttl": 300}, "celery": {"broker_url": "redis://localhost:6379/0", "result_backend": "redis://localhost:6379/0"}, "logging": {"level": "INFO", "file": "logs/app.log"}, "file_upload": {"max_size": "10MB", "allowed_types": ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]}, "localization": {"default_locale": "en", "fallback_locale": "en", "available_locales": ["en", "zh-CN"]}, "third_party": {"google_analytics_id": "{{GOOGLE_ANALYTICS_ID}}", "sentry_dsn": "{{SENTRY_DSN}}"}}