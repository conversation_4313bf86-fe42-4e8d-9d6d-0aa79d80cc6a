#!/usr/bin/env python3
"""
Quick Project Template Initializer
A simplified version for quick testing and demonstration.
"""

import os
import sys
import json
import shutil
import secrets
from pathlib import Path
from datetime import datetime


def replace_template_vars(content: str, vars_dict: dict) -> str:
    """Replace template variables in content."""
    for var, value in vars_dict.items():
        content = content.replace(f"{{{{{var}}}}}", str(value))
    return content


def process_file(src_path: Path, dst_path: Path, vars_dict: dict):
    """Process a single template file."""
    try:
        with open(src_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Process template variables
        processed_content = replace_template_vars(content, vars_dict)

        # Write to destination
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        with open(dst_path, "w", encoding="utf-8") as f:
            f.write(processed_content)

    except UnicodeDecodeError:
        # Binary file, copy as-is
        shutil.copy2(src_path, dst_path)


def main():
    if len(sys.argv) < 2:
        print("Usage: python quick-init.py <project_name>")
        sys.exit(1)

    project_name = sys.argv[1]
    project_slug = project_name.lower().replace(" ", "-").replace("_", "-")

    # Template variables
    template_vars = {
        "PROJECT_NAME": project_name,
        "PROJECT_TITLE": project_name,
        "PROJECT_DESCRIPTION": f"A Django + Vue.js application: {project_name}",
        "PROJECT_VERSION": "1.0.0",
        "PROJECT_SLUG": project_slug,
        "PROJECT_HOMEPAGE": "",
        "PROJECT_REPOSITORY": "",
        "PROJECT_LICENSE": "MIT",
        "PROJECT_LOGO": "",
        "AUTHOR_NAME": "Developer",
        "AUTHOR_EMAIL": "<EMAIL>",
        "AUTHOR_URL": "",
        "SECRET_KEY": secrets.token_urlsafe(50),
        "GENERATION_DATE": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "MODULE_NAME": "core",
        "DOMAIN": "localhost",
        "DEBUG": "True",
        "API_BASE_URL": "http://localhost:8000",
        "API_VERSION": "v1",
        "API_TIMEOUT": "30000",
        "API_TITLE": f"{project_name} API",
        "API_DESCRIPTION": f"API Documentation for {project_name}",
        "THEME_PRIMARY_COLOR": "#1890ff",
        "THEME_LAYOUT": "side",
        "THEME_DARK_MODE": "false",
        "THEME_COMPACT_MODE": "false",
        "ENABLE_API_DOCS": "true",
        "ENABLE_ADMIN_INTERFACE": "true",
        "ENABLE_DEBUG_TOOLBAR": "true",
        "ENABLE_CORS": "true",
        "ENABLE_EMAIL_NOTIFICATIONS": "false",
        "ENABLE_AUDIT_LOGGING": "true",
        "ENABLE_SYSTEM_NOTIFICATIONS": "true",
        "ENABLE_EMAIL_TEMPLATES": "true",
        "ENABLE_CACHING": "false",
        "ALLOWED_HOSTS": "localhost,127.0.0.1",
        "CORS_ALLOWED_ORIGINS": "http://localhost:5173,http://localhost:3000",
        "DEV_SERVER_HOST": "0.0.0.0",
        "DEV_SERVER_PORT": "5173",
        "DEV_SERVER_OPEN": "false",
        "DEVELOPMENT_SERVER_HOST": "0.0.0.0",
        "DEVELOPMENT_SERVER_PORT": "8000",
        "BUILD_SOURCEMAP": "false",
        "BUILD_ANALYZE": "false",
        "BUILD_COMPRESS": "true",
        "DB_USER": "root",
        "DB_PASSWORD": "password",
        "EMAIL_HOST": "smtp.gmail.com",
        "EMAIL_PORT": "587",
        "EMAIL_USE_TLS": "true",
        "EMAIL_HOST_USER": "",
        "EMAIL_HOST_PASSWORD": "",
        "DEFAULT_FROM_EMAIL": f"noreply@{project_slug}",
        "REDIS_URL": "redis://localhost:6379/1",
        "CACHE_TTL": "300",
        "CELERY_BROKER_URL": "redis://localhost:6379/0",
        "CELERY_RESULT_BACKEND": "redis://localhost:6379/0",
        "MEDIA_ROOT": "media",
        "STATIC_ROOT": "staticfiles",
        "MAX_UPLOAD_SIZE": "10485760",
        "MAX_FILE_SIZE": "10MB",
        "ALLOWED_FILE_TYPES": "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx",
        "LOG_LEVEL": "INFO",
        "LOG_FILE": "logs/app.log",
        "DEFAULT_LOCALE": "en",
        "FALLBACK_LOCALE": "en",
        "AVAILABLE_LOCALES": "en,zh-CN",
        "ENABLE_HTTPS": "false",
        "CSRF_TOKEN_NAME": "csrftoken",
        "GOOGLE_ANALYTICS_ID": "",
        "SENTRY_DSN": "",
        "ENABLE_PWA": "false",
        "ENABLE_MOCK": "false",
        "ENABLE_DEVTOOLS": "true",
        "ENABLE_I18N": "false",
        "ENABLE_ANALYTICS": "false",
        "ENABLE_USER_MANAGEMENT": "true",
        "ENABLE_AUDIT_LOG": "true",
        "ENABLE_NOTIFICATIONS": "false",
        "ENABLE_FILE_UPLOAD": "true",
        "ENABLE_EXPORT": "true",
    }

    # Paths
    template_dir = Path(__file__).parent
    target_dir = Path.cwd() / project_slug

    print(f"🎯 Creating project: {project_name}")
    print(f"📁 Target directory: {target_dir}")

    # Create target directory
    target_dir.mkdir(exist_ok=True)

    # Process template files
    print("📁 Processing template files...")

    for root, dirs, files in os.walk(template_dir):
        root_path = Path(root)

        # Skip scripts, docs, and generated project directories
        if any(
            skip in root_path.parts
            for skip in ["scripts", "docs", "__pycache__", "test-project"]
        ):
            continue

        rel_path = root_path.relative_to(template_dir)
        dst_dir = target_dir / rel_path

        for file in files:
            if file.endswith(".py") and file == "quick-init.py":
                continue

            src_file = Path(root) / file

            # Process filename
            dst_filename = file
            if dst_filename.endswith(".template"):
                dst_filename = dst_filename[:-9]  # Remove .template
            dst_filename = replace_template_vars(dst_filename, template_vars)

            dst_file = dst_dir / dst_filename

            print(
                f"  📄 {src_file.relative_to(template_dir)} -> {dst_file.relative_to(target_dir)}"
            )
            process_file(src_file, dst_file, template_vars)

    # Create startup scripts
    print("🚀 Creating startup scripts...")

    if os.name == "nt":  # Windows
        backend_script = target_dir / "start_backend.bat"
        with open(backend_script, "w") as f:
            f.write(
                f"""@echo off
echo Starting {project_name} Backend...
cd backend
call venv\\Scripts\\activate.bat
python manage.py runserver 0.0.0.0:8000
pause
"""
            )

        frontend_script = target_dir / "start_frontend.bat"
        with open(frontend_script, "w") as f:
            f.write(
                f"""@echo off
echo Starting {project_name} Frontend...
cd frontend
npm run dev
pause
"""
            )
    else:  # Unix-like
        backend_script = target_dir / "start_backend.sh"
        with open(backend_script, "w") as f:
            f.write(
                f"""#!/bin/bash
echo "Starting {project_name} Backend..."
cd backend
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
"""
            )
        os.chmod(backend_script, 0o755)

        frontend_script = target_dir / "start_frontend.sh"
        with open(frontend_script, "w") as f:
            f.write(
                f"""#!/bin/bash
echo "Starting {project_name} Frontend..."
cd frontend
npm run dev
"""
            )
        os.chmod(frontend_script, 0o755)

    print("\n✅ Project template created successfully!")
    print(f"📁 Project location: {target_dir}")
    print("\n🚀 Next steps:")
    print("1. cd " + project_slug)
    print("2. Set up backend:")
    print("   cd backend")
    print("   python -m venv venv")
    print("   # Activate venv and install requirements")
    print("   pip install -r requirements.txt")
    print("   python manage.py migrate")
    print("3. Set up frontend:")
    print("   cd frontend")
    print("   npm install")
    print("4. Start development servers")
    print("5. Open http://localhost:5173")


if __name__ == "__main__":
    main()
