"""
User models for My Awesome Project
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    """
    email = models.EmailField(_('email address'), unique=True)
    phone = models.CharField(_('phone number'), max_length=20, blank=True)
    avatar = models.ImageField(_('avatar'), upload_to='avatars/', blank=True, null=True)
    bio = models.TextField(_('biography'), max_length=500, blank=True)
    
    # Profile fields
    department = models.CharField(_('department'), max_length=100, blank=True)
    position = models.Char<PERSON>ield(_('position'), max_length=100, blank=True)
    
    # Settings
    is_email_verified = models.BooleanField(_('email verified'), default=False)
    is_phone_verified = models.<PERSON><PERSON>anField(_('phone verified'), default=False)
    
    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    last_login_ip = models.GenericIPAddressField(_('last login IP'), blank=True, null=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'my-awesome-project_users'
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        
    def __str__(self):
        return self.email or self.username
        
    @property
    def full_name(self):
        """Return the full name of the user."""
        return f"{self.first_name} {self.last_name}".strip() or self.username
        
    @property
    def display_name(self):
        """Return the display name for the user."""
        return self.full_name or self.email or self.username


class UserProfile(models.Model):
    """
    Extended user profile information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    
    # Additional profile fields
    timezone = models.CharField(_('timezone'), max_length=50, default='UTC')
    language = models.CharField(_('language'), max_length=10, default='en')
    theme = models.CharField(_('theme'), max_length=20, default='light')
    
    # Notification preferences
    email_notifications = models.BooleanField(_('email notifications'), default=True)
    sms_notifications = models.BooleanField(_('SMS notifications'), default=False)
    
    # Security settings
    two_factor_enabled = models.BooleanField(_('two-factor authentication'), default=False)
    two_factor_secret = models.CharField(_('2FA secret'), max_length=32, blank=True)
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        db_table = 'my-awesome-project_user_profiles'
        verbose_name = _('User Profile')
        verbose_name_plural = _('User Profiles')
        
    def __str__(self):
        return f"{self.user.display_name}'s Profile"
