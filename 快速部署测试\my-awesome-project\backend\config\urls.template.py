"""
My Awesome Project URL Configuration
Generated from template on 2025-08-02 10:52:43
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
import environ

# Initialize environment
env = environ.Env()

urlpatterns = [
    # Admin interface (if enabled)
    path('admin/', admin.site.urls) if env.bool('ENABLE_ADMIN_INTERFACE', default=True) else path('admin/', lambda request: None),
    
    # API endpoints
    path('api/auth/', include('apps.users.urls')),
    path('api/core/', include('apps.core.urls')),
]

# Conditional URL patterns based on feature flags

# API Documentation (if enabled)
if env.bool('ENABLE_API_DOCS', default=True):
    from drf_spectacular.views import (
        SpectacularAPIView,
        SpectacularRedocView,
        SpectacularSwaggerView,
    )
    
    urlpatterns += [
        path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
        path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
        path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    ]

# Audit logging URLs (if enabled)
if env.bool('ENABLE_AUDIT_LOGGING', default=True):
    urlpatterns += [
        path('api/audit/', include('apps.audit.urls')),
    ]

# Debug toolbar (if enabled and in debug mode)
if env.bool('ENABLE_DEBUG_TOOLBAR', default=False) and settings.DEBUG:
    import debug_toolbar
    urlpatterns += [
        path('__debug__/', include(debug_toolbar.urls)),
    ]

# Static and media files (for development)
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Custom error handlers
handler404 = 'core.views.custom_404'
handler500 = 'core.views.custom_500'

# Admin site customization
admin.site.site_header = "My Awesome Project Administration"
admin.site.site_title = "My Awesome Project Admin"
admin.site.index_title = "Welcome to My Awesome Project Administration"
