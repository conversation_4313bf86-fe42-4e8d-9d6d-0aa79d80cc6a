"""
Audit views for 快速部署测试
Provides comprehensive audit logging and monitoring endpoints
"""

from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Count, Q
from django.http import HttpResponse
from datetime import datetime, timedelta
import csv
import json
import io

from .models import OperationLog, ItemAccessLog, SecurityEvent
from .serializers import (
    OperationLogSerializer,
    ItemAccessLogSerializer,
    SecurityEventSerializer,
    AuditStatsSerializer,
    UserActivitySerializer,
    AuditSearchSerializer,
    SecurityEventCreateSerializer,
    SecurityEventResolveSerializer,
    AuditExportSerializer,
)

User = get_user_model()


@extend_schema_view(
    get=extend_schema(
        summary="Get Operation Logs",
        description="Get system operation logs with pagination and filtering. Non-admin users can only view their own logs.",
        parameters=[
            OpenApiParameter(
                name="start_time",
                type=OpenApiTypes.DATETIME,
                location=OpenApiParameter.QUERY,
                description="Start time filter (ISO 8601 format)",
            ),
            OpenApiParameter(
                name="end_time",
                type=OpenApiTypes.DATETIME,
                location=OpenApiParameter.QUERY,
                description="End time filter (ISO 8601 format)",
            ),
            OpenApiParameter(
                name="action_type",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Action type filter",
            ),
            OpenApiParameter(
                name="target_type",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Target object type filter",
            ),
            OpenApiParameter(
                name="result",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Operation result filter (success/failure)",
            ),
        ],
        tags=["Audit Logs"],
    )
)
class OperationLogListView(generics.ListAPIView):
    """Operation log list view"""

    serializer_class = OperationLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get operation log queryset"""
        queryset = OperationLog.objects.select_related("user").order_by("-created_at")

        # Non-admin users can only view their own logs
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        # Apply search filters
        return self.apply_filters(queryset)

    def apply_filters(self, queryset):
        """Apply search filters"""
        user_id = self.request.query_params.get("user_id")
        action = self.request.query_params.get("action")
        resource_type = self.request.query_params.get("resource_type")
        resource_id = self.request.query_params.get("resource_id")
        start_time = self.request.query_params.get("start_time")
        end_time = self.request.query_params.get("end_time")
        keyword = self.request.query_params.get("keyword")

        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if action:
            queryset = queryset.filter(action_type=action)

        if resource_type:
            queryset = queryset.filter(target_type=resource_type)

        if resource_id:
            queryset = queryset.filter(target_id=resource_id)

        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                pass

        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__lte=end_dt)
            except ValueError:
                pass

        if keyword:
            queryset = queryset.filter(
                Q(description__icontains=keyword)
                | Q(user__email__icontains=keyword)
                | Q(user__first_name__icontains=keyword)
                | Q(user__last_name__icontains=keyword)
            )

        return queryset


@extend_schema_view(
    get=extend_schema(
        summary="Get Item Access Logs",
        description="Get item access logs with pagination and filtering.",
        tags=["Audit Logs"],
    )
)
class ItemAccessLogListView(generics.ListAPIView):
    """Item access log list view"""

    serializer_class = ItemAccessLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get item access log queryset"""
        queryset = ItemAccessLog.objects.select_related("user").order_by("-created_at")

        # Non-admin users can only view their own logs
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        # Apply search filters
        return self.apply_filters(queryset)

    def apply_filters(self, queryset):
        """Apply search filters"""
        user_id = self.request.query_params.get("user_id")
        action = self.request.query_params.get("action")
        content_type = self.request.query_params.get("content_type")
        object_id = self.request.query_params.get("object_id")
        start_time = self.request.query_params.get("start_time")
        end_time = self.request.query_params.get("end_time")
        ip_address = self.request.query_params.get("ip_address")

        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if action:
            queryset = queryset.filter(access_type=action)

        if content_type:
            queryset = queryset.filter(content_type=content_type)

        if object_id:
            queryset = queryset.filter(object_id=object_id)

        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                pass

        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__lte=end_dt)
            except ValueError:
                pass

        if ip_address:
            queryset = queryset.filter(ip_address=ip_address)

        return queryset


@extend_schema_view(
    get=extend_schema(
        summary="Get Security Events",
        description="Get security events list with filtering and pagination.",
        tags=["Security"],
    ),
    post=extend_schema(
        summary="Create Security Event",
        description="Create a new security event.",
        tags=["Security"],
    ),
)
class SecurityEventListCreateView(generics.ListCreateAPIView):
    """Security event list and create view"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "POST":
            return SecurityEventCreateSerializer
        return SecurityEventSerializer

    def get_queryset(self):
        """Get security events queryset"""
        queryset = SecurityEvent.objects.select_related("user", "assigned_to").order_by(
            "-created_at"
        )

        # Apply filters
        return self.apply_filters(queryset)

    def apply_filters(self, queryset):
        """Apply search filters"""
        event_type = self.request.query_params.get("event_type")
        severity = self.request.query_params.get("severity")
        status = self.request.query_params.get("status")
        user_id = self.request.query_params.get("user_id")
        start_time = self.request.query_params.get("start_time")
        end_time = self.request.query_params.get("end_time")

        if event_type:
            queryset = queryset.filter(event_type=event_type)

        if severity:
            queryset = queryset.filter(severity=severity)

        if status:
            queryset = queryset.filter(status=status)

        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                pass

        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
                queryset = queryset.filter(created_at__lte=end_dt)
            except ValueError:
                pass

        return queryset


@extend_schema_view(
    get=extend_schema(
        summary="Get Security Event Detail",
        description="Get detailed information about a security event.",
        tags=["Security"],
    ),
    patch=extend_schema(
        summary="Update Security Event",
        description="Update security event information.",
        tags=["Security"],
    ),
)
class SecurityEventDetailView(generics.RetrieveUpdateAPIView):
    """Security event detail view"""

    queryset = SecurityEvent.objects.all()
    serializer_class = SecurityEventSerializer
    permission_classes = [permissions.IsAuthenticated]


@extend_schema(
    summary="Resolve Security Event",
    description="Mark a security event as resolved.",
    tags=["Security"],
)
@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def resolve_security_event(request, event_id):
    """Resolve a security event"""
    event = get_object_or_404(SecurityEvent, id=event_id)

    serializer = SecurityEventResolveSerializer(
        event, data=request.data, context={"request": request}
    )

    if serializer.is_valid():
        serializer.save()
        return Response(SecurityEventSerializer(event).data)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="Get Audit Statistics",
    description="Get comprehensive audit statistics and analytics.",
    tags=["Audit Analytics"],
)
@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def audit_stats(request):
    """Get audit statistics"""
    # Basic counts
    total_operations = OperationLog.objects.count()
    total_accesses = ItemAccessLog.objects.count()
    total_security_events = SecurityEvent.objects.count()
    unresolved_security_events = SecurityEvent.objects.filter(status="open").count()

    # Time-based statistics (last 24 hours)
    now = timezone.now()
    last_24h = now - timedelta(hours=24)

    operations_by_hour = []
    for i in range(24):
        hour_start = last_24h + timedelta(hours=i)
        hour_end = hour_start + timedelta(hours=1)
        count = OperationLog.objects.filter(
            created_at__gte=hour_start, created_at__lt=hour_end
        ).count()
        operations_by_hour.append(
            {"hour": hour_start.strftime("%H:00"), "count": count}
        )

    # Top active users (last 7 days)
    last_week = now - timedelta(days=7)
    top_active_users = (
        OperationLog.objects.filter(created_at__gte=last_week)
        .values("user__email", "user__first_name", "user__last_name")
        .annotate(count=Count("id"))
        .order_by("-count")[:10]
    )

    # Operations by action type
    operations_by_action = (
        OperationLog.objects.values("action_type")
        .annotate(count=Count("id"))
        .order_by("-count")[:10]
    )

    # Security events by type
    security_events_by_type = (
        SecurityEvent.objects.values("event_type")
        .annotate(count=Count("id"))
        .order_by("-count")
    )

    # Security events by severity
    security_events_by_severity = (
        SecurityEvent.objects.values("severity")
        .annotate(count=Count("id"))
        .order_by("-count")
    )

    stats_data = {
        "total_operations": total_operations,
        "total_accesses": total_accesses,
        "total_security_events": total_security_events,
        "unresolved_security_events": unresolved_security_events,
        "operations_by_hour": operations_by_hour,
        "operations_by_day": [],  # Can be implemented for longer periods
        "top_active_users": list(top_active_users),
        "operations_by_action": list(operations_by_action),
        "operations_by_resource": [],  # Can be customized based on business objects
        "security_events_by_type": list(security_events_by_type),
        "security_events_by_severity": list(security_events_by_severity),
    }

    serializer = AuditStatsSerializer(stats_data)
    return Response(serializer.data)


@extend_schema(
    summary="Export Audit Logs",
    description="Export audit logs in various formats (CSV, Excel, JSON).",
    tags=["Audit Export"],
)
@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def export_audit_logs(request):
    """Export audit logs"""
    serializer = AuditExportSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    data = serializer.validated_data
    log_type = data["log_type"]
    export_format = data["format"]
    start_time = data.get("start_time")
    end_time = data.get("end_time")
    user_ids = data.get("user_ids", [])
    include_details = data.get("include_details", True)

    # Get queryset based on log type
    if log_type == "operation":
        queryset = OperationLog.objects.select_related("user")
    elif log_type == "access":
        queryset = ItemAccessLog.objects.select_related("user")
    elif log_type == "security":
        queryset = SecurityEvent.objects.select_related("user", "assigned_to")
    else:
        return Response(
            {"error": "Invalid log type"}, status=status.HTTP_400_BAD_REQUEST
        )

    # Apply filters
    if start_time:
        queryset = queryset.filter(created_at__gte=start_time)
    if end_time:
        queryset = queryset.filter(created_at__lte=end_time)
    if user_ids:
        queryset = queryset.filter(user_id__in=user_ids)

    # Limit queryset size for performance
    queryset = queryset.order_by("-created_at")[:10000]

    # Generate export based on format
    if export_format == "csv":
        return export_csv(queryset, log_type, include_details)
    elif export_format == "json":
        return export_json(queryset, log_type, include_details)
    else:
        return Response(
            {"error": "Unsupported format"}, status=status.HTTP_400_BAD_REQUEST
        )


def export_csv(queryset, log_type, include_details):
    """Export data as CSV"""
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = f'attachment; filename="{log_type}_logs.csv"'

    writer = csv.writer(response)

    # Write headers based on log type
    if log_type == "operation":
        headers = [
            "ID",
            "User",
            "Action",
            "Result",
            "Target",
            "IP Address",
            "Created At",
        ]
        if include_details:
            headers.extend(["Description", "User Agent", "Request Path"])
        writer.writerow(headers)

        for log in queryset:
            row = [
                str(log.id),
                log.user.email if log.user else "Anonymous",
                log.get_action_type_display(),
                log.get_result_display(),
                f"{log.target_type}:{log.target_name}" if log.target_type else "",
                log.ip_address,
                log.created_at.isoformat(),
            ]
            if include_details:
                row.extend([log.description, log.user_agent, log.request_path])
            writer.writerow(row)

    elif log_type == "access":
        headers = ["ID", "User", "Object", "Access Type", "IP Address", "Created At"]
        if include_details:
            headers.extend(["User Agent", "Access Source"])
        writer.writerow(headers)

        for log in queryset:
            row = [
                str(log.id),
                log.user.email if log.user else "Anonymous",
                f"{log.content_type}:{log.object_name}",
                log.get_access_type_display(),
                log.ip_address,
                log.created_at.isoformat(),
            ]
            if include_details:
                row.extend([log.user_agent, log.access_source])
            writer.writerow(row)

    elif log_type == "security":
        headers = [
            "ID",
            "Event Type",
            "Severity",
            "Status",
            "Title",
            "User",
            "Created At",
        ]
        if include_details:
            headers.extend(["Description", "Assigned To", "Resolved At"])
        writer.writerow(headers)

        for event in queryset:
            row = [
                str(event.id),
                event.get_event_type_display(),
                event.get_severity_display(),
                event.get_status_display(),
                event.title,
                event.user.email if event.user else "",
                event.created_at.isoformat(),
            ]
            if include_details:
                row.extend(
                    [
                        event.description,
                        event.assigned_to.email if event.assigned_to else "",
                        event.resolved_at.isoformat() if event.resolved_at else "",
                    ]
                )
            writer.writerow(row)

    return response


def export_json(queryset, log_type, include_details):
    """Export data as JSON"""
    # Use appropriate serializer based on log type
    if log_type == "operation":
        serializer = OperationLogSerializer(queryset, many=True)
    elif log_type == "access":
        serializer = ItemAccessLogSerializer(queryset, many=True)
    elif log_type == "security":
        serializer = SecurityEventSerializer(queryset, many=True)

    response = HttpResponse(content_type="application/json")
    response["Content-Disposition"] = f'attachment; filename="{log_type}_logs.json"'

    json.dump(serializer.data, response, indent=2, default=str)
    return response
