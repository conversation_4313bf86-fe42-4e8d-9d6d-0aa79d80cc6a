#!/bin/bash

# 导出Docker镜像脚本 - 用于离线部署

set -e

echo "📦 导出{{PROJECT_NAME}}基础镜像"
echo "用于内网环境离线部署"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 镜像信息
BACKEND_IMAGE="locker-template-backend-base:latest"
FRONTEND_IMAGE="locker-template-frontend-base:latest"
EXPORT_DIR="./docker-images"

# 创建导出目录
mkdir -p $EXPORT_DIR

echo "🔍 检查镜像是否存在..."

# 检查后端镜像
if ! docker image inspect $BACKEND_IMAGE > /dev/null 2>&1; then
    echo "❌ 后端镜像不存在: $BACKEND_IMAGE"
    echo "请先运行构建脚本: ./scripts/build-images.sh"
    exit 1
fi

# 检查前端镜像
if ! docker image inspect $FRONTEND_IMAGE > /dev/null 2>&1; then
    echo "❌ 前端镜像不存在: $FRONTEND_IMAGE"
    echo "请先运行构建脚本: ./scripts/build-images.sh"
    exit 1
fi

echo "✅ 镜像检查通过"

# 导出后端镜像
echo "📤 导出后端镜像..."
docker save $BACKEND_IMAGE | gzip > $EXPORT_DIR/backend-base.tar.gz
BACKEND_SIZE=$(du -h $EXPORT_DIR/backend-base.tar.gz | cut -f1)
echo "✅ 后端镜像导出完成: $EXPORT_DIR/backend-base.tar.gz ($BACKEND_SIZE)"

# 导出前端镜像
echo "📤 导出前端镜像..."
docker save $FRONTEND_IMAGE | gzip > $EXPORT_DIR/frontend-base.tar.gz
FRONTEND_SIZE=$(du -h $EXPORT_DIR/frontend-base.tar.gz | cut -f1)
echo "✅ 前端镜像导出完成: $EXPORT_DIR/frontend-base.tar.gz ($FRONTEND_SIZE)"

# 导出依赖镜像
echo "📤 导出依赖镜像..."
docker save postgres:15-alpine | gzip > $EXPORT_DIR/postgres.tar.gz
docker save redis:7-alpine | gzip > $EXPORT_DIR/redis.tar.gz

POSTGRES_SIZE=$(du -h $EXPORT_DIR/postgres.tar.gz | cut -f1)
REDIS_SIZE=$(du -h $EXPORT_DIR/redis.tar.gz | cut -f1)

echo "✅ PostgreSQL镜像导出完成: $EXPORT_DIR/postgres.tar.gz ($POSTGRES_SIZE)"
echo "✅ Redis镜像导出完成: $EXPORT_DIR/redis.tar.gz ($REDIS_SIZE)"

# 创建导入脚本
echo "📝 创建导入脚本..."
cat > $EXPORT_DIR/import-images.sh << 'EOF'
#!/bin/bash

# 导入Docker镜像脚本

set -e

echo "📥 导入{{PROJECT_NAME}}基础镜像"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 导入镜像
echo "📥 导入后端基础镜像..."
docker load < backend-base.tar.gz

echo "📥 导入前端基础镜像..."
docker load < frontend-base.tar.gz

echo "📥 导入PostgreSQL镜像..."
docker load < postgres.tar.gz

echo "📥 导入Redis镜像..."
docker load < redis.tar.gz

echo ""
echo "✅ 所有镜像导入完成！"
echo ""
echo "📋 导入的镜像："
docker images | grep -E "(locker-template|postgres|redis)"

echo ""
echo "🚀 下一步："
echo "1. 将项目代码复制到目标环境"
echo "2. 运行快速部署脚本: ./scripts/quick-deploy.sh"
EOF

chmod +x $EXPORT_DIR/import-images.sh

# 创建Windows导入脚本
cat > $EXPORT_DIR/import-images.bat << 'EOF'
@echo off
chcp 65001 >nul

echo 📥 导入{{PROJECT_NAME}}基础镜像
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 导入镜像
echo 📥 导入后端基础镜像...
docker load < backend-base.tar.gz

echo 📥 导入前端基础镜像...
docker load < frontend-base.tar.gz

echo 📥 导入PostgreSQL镜像...
docker load < postgres.tar.gz

echo 📥 导入Redis镜像...
docker load < redis.tar.gz

echo.
echo ✅ 所有镜像导入完成！
echo.
echo 📋 导入的镜像：
docker images | findstr /C:"locker-template" /C:"postgres" /C:"redis"

echo.
echo 🚀 下一步：
echo 1. 将项目代码复制到目标环境
echo 2. 运行快速部署脚本: scripts\quick-deploy.bat
echo.
pause
EOF

# 创建README文件
cat > $EXPORT_DIR/README.md << 'EOF'
# {{PROJECT_NAME}} Docker镜像包

本目录包含{{PROJECT_NAME}}项目的所有Docker镜像，用于离线部署。

## 📦 包含的镜像

- `backend-base.tar.gz` - 后端基础镜像（包含所有Python依赖）
- `frontend-base.tar.gz` - 前端基础镜像（包含所有Node.js依赖）
- `postgres.tar.gz` - PostgreSQL数据库镜像
- `redis.tar.gz` - Redis缓存镜像

## 🚀 使用方法

### 1. 导入镜像

**Linux/macOS:**
```bash
chmod +x import-images.sh
./import-images.sh
```

**Windows:**
```bash
import-images.bat
```

### 2. 部署项目

将项目代码复制到目标环境，然后运行：

**Linux/macOS:**
```bash
./scripts/quick-deploy.sh
```

**Windows:**
```bash
scripts\quick-deploy.bat
```

## 📋 系统要求

- Docker 20.0+
- Docker Compose 2.0+
- 至少 4GB 可用磁盘空间
- 至少 2GB 可用内存

## 🔧 故障排除

如果导入失败，请检查：
1. Docker是否正常运行
2. 磁盘空间是否充足
3. 镜像文件是否完整

## 📚 更多信息

详细文档请参考项目中的 `docs/DOCKER_IMAGES.md` 文件。
EOF

# 显示导出结果
echo ""
echo "🎉 镜像导出完成！"
echo ""
echo "📁 导出目录: $EXPORT_DIR"
echo "📋 导出的文件："
ls -lh $EXPORT_DIR/
echo ""
echo "📦 总大小: $(du -sh $EXPORT_DIR | cut -f1)"
echo ""
echo "🚀 使用方法："
echo "1. 将 $EXPORT_DIR 目录复制到目标环境"
echo "2. 在目标环境运行: cd $EXPORT_DIR && ./import-images.sh"
echo "3. 复制项目代码到目标环境"
echo "4. 运行快速部署脚本"
