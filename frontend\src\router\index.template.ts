import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/store/auth'

// Layout components
const Layout = () => import('@/components/Layout/index.vue')

// Page components
const Login = () => import('@/views/auth/Login.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const Profile = () => import('@/views/user/Profile.vue')
const UserManagement = () => import('@/views/admin/UserManagement.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: 'Login - {{PROJECT_NAME}}'
    }
  },
  {
    path: '/',
    component: Layout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard - {{PROJECT_NAME}}',
          icon: 'dashboard'
        }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: Profile,
        meta: {
          title: 'Profile - {{PROJECT_NAME}}',
          icon: 'user'
        }
      },
      {
        path: '/admin',
        name: 'Admin',
        meta: {
          title: 'Administration',
          icon: 'setting',
          requiresAdmin: true
        },
        children: [
          {
            path: 'users',
            name: 'UserManagement',
            component: UserManagement,
            meta: {
              title: 'User Management - {{PROJECT_NAME}}',
              icon: 'team'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: 'Page Not Found - {{PROJECT_NAME}}'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Set page title
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
  
  // Check authentication
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // Try to restore authentication from token
      const restored = await authStore.restoreAuth()
      if (!restored) {
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
      }
    }
    
    // Check admin permission
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next({ name: 'Dashboard' })
      return
    }
  } else if (authStore.isAuthenticated && to.name === 'Login') {
    // Redirect to dashboard if already logged in
    next({ name: 'Dashboard' })
    return
  }
  
  next()
})

export default router
