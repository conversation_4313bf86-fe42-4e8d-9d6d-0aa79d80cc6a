# 后端基础镜像 - 预安装所有Python依赖
FROM python:3.12.11-bullseye

# 镜像元数据
LABEL maintainer="{{PROJECT_NAME}} Team"
LABEL description="{{PROJECT_NAME}} Backend Base Image"
LABEL version="1.0"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DJANGO_SETTINGS_MODULE=config.settings \
    PYTHONPATH=/app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制requirements文件
COPY requirements.txt /app/requirements.txt

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# 创建应用用户
RUN useradd --create-home --shell /bin/bash app

# 创建必要的目录
RUN mkdir -p /app/logs /app/media /app/static /app/staticfiles && \
    chown -R app:app /app

# 创建简单的启动脚本
RUN echo '#!/bin/bash\nset -e\necho "Starting Django application..."\nexec "$@"' > /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh && \
    chown app:app /app/entrypoint.sh

# 切换到应用用户
USER app

# 暴露端口
EXPOSE 8000

# 设置入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
