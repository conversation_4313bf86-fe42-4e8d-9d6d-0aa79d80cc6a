# 前端基础镜像 - 预安装所有Node.js依赖
FROM node:lts-bullseye

# 镜像元数据
LABEL maintainer="{{PROJECT_NAME}} Team"
LABEL description="{{PROJECT_NAME}} Frontend Base Image"
LABEL version="1.0"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=development \
    VITE_DEV_SERVER_HOST=0.0.0.0 \
    VITE_DEV_SERVER_PORT=5173

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    python3 \
    make \
    g++ \
    curl \
    bash \
    && rm -rf /var/lib/apt/lists/*

# 复制package文件
COPY package.json /app/package.json

# 安装Node.js依赖
RUN npm install && \
    npm cache clean --force

# 创建非root用户
RUN groupadd -g 1001 nodejs && \
    useradd -u 1001 -g nodejs -m -s /bin/bash vueapp

# 创建启动脚本
RUN echo '#!/bin/bash' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo 'echo "启动前端开发服务器..."' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 检查package.json是否存在' >> /app/entrypoint.sh && \
    echo 'if [ ! -f package.json ]; then' >> /app/entrypoint.sh && \
    echo '    echo "错误: package.json 文件不存在"' >> /app/entrypoint.sh && \
    echo '    exit 1' >> /app/entrypoint.sh && \
    echo 'fi' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 安装可能的新依赖' >> /app/entrypoint.sh && \
    echo 'if [ -f package-lock.json ]; then' >> /app/entrypoint.sh && \
    echo '    npm ci' >> /app/entrypoint.sh && \
    echo 'else' >> /app/entrypoint.sh && \
    echo '    npm install' >> /app/entrypoint.sh && \
    echo 'fi' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 启动开发服务器' >> /app/entrypoint.sh && \
    echo 'exec "$@"' >> /app/entrypoint.sh

RUN chmod +x /app/entrypoint.sh

# 设置权限
RUN chown -R vueapp:nodejs /app

# 切换到应用用户
USER vueapp

# 暴露端口
EXPOSE 5173

# 设置入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令
CMD ["npm", "run", "dev"]
