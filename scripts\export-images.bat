@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 📦 导出{{PROJECT_NAME}}基础镜像
echo 用于内网环境离线部署
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 镜像信息
set BACKEND_IMAGE=locker-template-backend-base:latest
set FRONTEND_IMAGE=locker-template-frontend-base:latest
set EXPORT_DIR=docker-images

REM 创建导出目录
if not exist %EXPORT_DIR% mkdir %EXPORT_DIR%

echo 🔍 检查镜像是否存在...

REM 检查后端镜像
docker image inspect %BACKEND_IMAGE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 后端镜像不存在: %BACKEND_IMAGE%
    echo 请先运行构建脚本: scripts\build-images.bat
    pause
    exit /b 1
)

REM 检查前端镜像
docker image inspect %FRONTEND_IMAGE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端镜像不存在: %FRONTEND_IMAGE%
    echo 请先运行构建脚本: scripts\build-images.bat
    pause
    exit /b 1
)

echo ✅ 镜像检查通过

REM 导出后端镜像
echo 📤 导出后端镜像...
docker save %BACKEND_IMAGE% | gzip > %EXPORT_DIR%\backend-base.tar.gz
echo ✅ 后端镜像导出完成: %EXPORT_DIR%\backend-base.tar.gz

REM 导出前端镜像
echo 📤 导出前端镜像...
docker save %FRONTEND_IMAGE% | gzip > %EXPORT_DIR%\frontend-base.tar.gz
echo ✅ 前端镜像导出完成: %EXPORT_DIR%\frontend-base.tar.gz

REM 导出依赖镜像
echo 📤 导出依赖镜像...
docker save postgres:15-alpine | gzip > %EXPORT_DIR%\postgres.tar.gz
docker save redis:7-alpine | gzip > %EXPORT_DIR%\redis.tar.gz

echo ✅ PostgreSQL镜像导出完成: %EXPORT_DIR%\postgres.tar.gz
echo ✅ Redis镜像导出完成: %EXPORT_DIR%\redis.tar.gz

REM 创建导入脚本
echo 📝 创建导入脚本...
echo @echo off > %EXPORT_DIR%\import-images.bat
echo chcp 65001 ^>nul >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo echo 📥 导入{{PROJECT_NAME}}基础镜像 >> %EXPORT_DIR%\import-images.bat
echo echo. >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo REM 检查Docker是否运行 >> %EXPORT_DIR%\import-images.bat
echo docker info ^>nul 2^>^&1 >> %EXPORT_DIR%\import-images.bat
echo if errorlevel 1 ^( >> %EXPORT_DIR%\import-images.bat
echo     echo ❌ Docker未运行，请先启动Docker >> %EXPORT_DIR%\import-images.bat
echo     pause >> %EXPORT_DIR%\import-images.bat
echo     exit /b 1 >> %EXPORT_DIR%\import-images.bat
echo ^) >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo REM 导入镜像 >> %EXPORT_DIR%\import-images.bat
echo echo 📥 导入后端基础镜像... >> %EXPORT_DIR%\import-images.bat
echo docker load ^< backend-base.tar.gz >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo echo 📥 导入前端基础镜像... >> %EXPORT_DIR%\import-images.bat
echo docker load ^< frontend-base.tar.gz >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo echo 📥 导入PostgreSQL镜像... >> %EXPORT_DIR%\import-images.bat
echo docker load ^< postgres.tar.gz >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo echo 📥 导入Redis镜像... >> %EXPORT_DIR%\import-images.bat
echo docker load ^< redis.tar.gz >> %EXPORT_DIR%\import-images.bat
echo. >> %EXPORT_DIR%\import-images.bat
echo echo. >> %EXPORT_DIR%\import-images.bat
echo echo ✅ 所有镜像导入完成！ >> %EXPORT_DIR%\import-images.bat
echo echo. >> %EXPORT_DIR%\import-images.bat
echo echo 🚀 下一步： >> %EXPORT_DIR%\import-images.bat
echo echo 1. 将项目代码复制到目标环境 >> %EXPORT_DIR%\import-images.bat
echo echo 2. 运行快速部署脚本: scripts\quick-deploy.bat >> %EXPORT_DIR%\import-images.bat
echo echo. >> %EXPORT_DIR%\import-images.bat
echo pause >> %EXPORT_DIR%\import-images.bat

REM 创建README文件
echo # {{PROJECT_NAME}} Docker镜像包 > %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo 本目录包含{{PROJECT_NAME}}项目的所有Docker镜像，用于离线部署。 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ## 📦 包含的镜像 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo - `backend-base.tar.gz` - 后端基础镜像（包含所有Python依赖） >> %EXPORT_DIR%\README.md
echo - `frontend-base.tar.gz` - 前端基础镜像（包含所有Node.js依赖） >> %EXPORT_DIR%\README.md
echo - `postgres.tar.gz` - PostgreSQL数据库镜像 >> %EXPORT_DIR%\README.md
echo - `redis.tar.gz` - Redis缓存镜像 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ## 🚀 使用方法 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ### 1. 导入镜像 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ```bash >> %EXPORT_DIR%\README.md
echo import-images.bat >> %EXPORT_DIR%\README.md
echo ``` >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ### 2. 部署项目 >> %EXPORT_DIR%\README.md
echo. >> %EXPORT_DIR%\README.md
echo ```bash >> %EXPORT_DIR%\README.md
echo scripts\quick-deploy.bat >> %EXPORT_DIR%\README.md
echo ``` >> %EXPORT_DIR%\README.md

REM 显示导出结果
echo.
echo 🎉 镜像导出完成！
echo.
echo 📁 导出目录: %EXPORT_DIR%
echo 📋 导出的文件：
dir %EXPORT_DIR%
echo.
echo 🚀 使用方法：
echo 1. 将 %EXPORT_DIR% 目录复制到目标环境
echo 2. 在目标环境运行: cd %EXPORT_DIR% ^&^& import-images.bat
echo 3. 复制项目代码到目标环境
echo 4. 运行快速部署脚本
echo.
pause
