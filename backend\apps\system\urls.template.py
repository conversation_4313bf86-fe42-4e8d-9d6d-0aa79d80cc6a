"""
System URLs for {{PROJECT_NAME}}
Based on the existing system management functionality
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'system'

# System settings URLs
settings_urlpatterns = [
    path("settings/", views.SystemSettingListView.as_view(), name="setting_list"),
    path("settings/<str:key>/", views.SystemSettingDetailView.as_view(), name="setting_detail"),
    path("settings/bulk/update/", views.BulkSettingsUpdateView.as_view(), name="bulk_settings_update"),
]

# Email template URLs
email_urlpatterns = [
    path("email-templates/", views.EmailTemplateListCreateView.as_view(), name="email_template_list_create"),
    path("email-templates/<uuid:pk>/", views.EmailTemplateDetailView.as_view(), name="email_template_detail"),
]

# Notification URLs
notification_urlpatterns = [
    path("notifications/", views.SystemNotificationListCreateView.as_view(), name="notification_list_create"),
    path("notifications/<uuid:pk>/", views.SystemNotificationDetailView.as_view(), name="notification_detail"),
    path("notifications/user/", views.UserNotificationListView.as_view(), name="user_notification_list"),
    path("notifications/<uuid:notification_id>/read/", views.mark_notification_read, name="mark_notification_read"),
    path("notifications/<uuid:notification_id>/dismiss/", views.dismiss_notification, name="dismiss_notification"),
]

# System monitoring URLs
monitoring_urlpatterns = [
    path("stats/", views.system_stats, name="system_stats"),
    path("health/", views.system_health, name="system_health"),
]

# Combine all URL patterns
urlpatterns = [
    path("", include(settings_urlpatterns)),
    path("", include(email_urlpatterns)),
    path("", include(notification_urlpatterns)),
    path("", include(monitoring_urlpatterns)),
]

# DRF Router (for future ViewSets if needed)
router = DefaultRouter()
# router.register(r'settings', SystemSettingViewSet)

urlpatterns += router.urls
