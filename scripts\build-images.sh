#!/bin/bash

# 构建前端和后端基础镜像脚本

set -e

echo "🏗️ 开始构建项目基础镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker Desktop"
    exit 1
fi

# 项目信息
PROJECT_NAME="locker-template"
BACKEND_IMAGE="${PROJECT_NAME}-backend-base"
FRONTEND_IMAGE="${PROJECT_NAME}-frontend-base"
VERSION="latest"

echo "📦 项目信息:"
echo "  项目名称: ${PROJECT_NAME}"
echo "  后端镜像: ${BACKEND_IMAGE}:${VERSION}"
echo "  前端镜像: ${FRONTEND_IMAGE}:${VERSION}"
echo ""

# 构建后端镜像
echo "🔨 构建后端基础镜像..."
cd backend
docker build \
    --tag ${BACKEND_IMAGE}:${VERSION} \
    --tag ${BACKEND_IMAGE}:$(date +%Y%m%d) \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo "✅ 后端镜像构建成功: ${BACKEND_IMAGE}:${VERSION}"
else
    echo "❌ 后端镜像构建失败"
    exit 1
fi

cd ..

# 构建前端镜像
echo "🔨 构建前端基础镜像..."
cd frontend
docker build \
    --tag ${FRONTEND_IMAGE}:${VERSION} \
    --tag ${FRONTEND_IMAGE}:$(date +%Y%m%d) \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo "✅ 前端镜像构建成功: ${FRONTEND_IMAGE}:${VERSION}"
else
    echo "❌ 前端镜像构建失败"
    exit 1
fi

cd ..

# 显示构建的镜像
echo ""
echo "📋 构建完成的镜像:"
docker images | grep ${PROJECT_NAME}

# 显示镜像大小
echo ""
echo "📊 镜像大小信息:"
echo "后端镜像大小:"
docker images ${BACKEND_IMAGE}:${VERSION} --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
echo "前端镜像大小:"
docker images ${FRONTEND_IMAGE}:${VERSION} --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

echo ""
echo "🎉 所有镜像构建完成！"
echo ""
echo "💡 使用方法:"
echo "  1. 在项目模板中使用这些基础镜像"
echo "  2. 修改 docker-compose.yml 中的镜像名称"
echo "  3. 运行 docker-compose up 启动项目"
echo ""
echo "🔧 镜像管理命令:"
echo "  查看镜像: docker images | grep ${PROJECT_NAME}"
echo "  删除镜像: docker rmi ${BACKEND_IMAGE}:${VERSION} ${FRONTEND_IMAGE}:${VERSION}"
echo "  推送镜像: docker push ${BACKEND_IMAGE}:${VERSION}"
echo "  推送镜像: docker push ${FRONTEND_IMAGE}:${VERSION}"
