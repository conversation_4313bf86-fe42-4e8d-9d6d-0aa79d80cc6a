# {{PROJECT_NAME}} - 模块说明文档

本文档说明了项目模板中包含的各个后端模块的功能和配置方法。

## 模块概览

### 1. 审计模块 (Audit)
**路径**: `apps/audit/`
**功能**: 提供全面的审计日志和安全监控功能

#### 主要功能
- **操作日志**: 记录用户的所有操作行为
- **登录日志**: 跟踪用户登录/登出记录
- **访问日志**: 记录数据访问和查看行为
- **安全事件**: 监控和记录安全相关事件

#### 配置选项
```python
# 在 .env 文件中配置
ENABLE_AUDIT_LOGGING=true  # 启用审计日志功能
```

#### API 端点
- `GET /api/audit/operation-logs/` - 获取操作日志
- `GET /api/audit/access-logs/` - 获取访问日志
- `GET /api/audit/security-events/` - 获取安全事件
- `GET /api/audit/stats/` - 获取审计统计信息
- `POST /api/audit/export/` - 导出审计日志

#### 使用示例
```python
# 手动记录操作日志
from apps.audit.signals import log_custom_action

log_custom_action(
    user=request.user,
    action_type="item_create",
    description="创建了新的密码项",
    target_type="password_item",
    target_id="123",
    target_name="我的银行密码",
    request=request
)
```

### 2. 用户组和团队管理 (Users)
**路径**: `apps/users/`
**功能**: 扩展的用户管理系统，包含组织架构

#### 主要功能
- **用户管理**: 用户创建、更新、删除
- **部门管理**: 组织部门结构
- **团队管理**: 项目团队组织
- **角色管理**: 权限角色分配
- **多因素认证**: MFA 支持

#### 模型结构
- `User`: 扩展的用户模型
- `Department`: 部门模型
- `Team`: 团队模型
- `Role`: 角色模型

#### API 端点
- `POST /api/auth/login/` - 用户登录
- `POST /api/auth/logout/` - 用户登出
- `GET /api/auth/users/` - 用户列表
- `GET /api/auth/departments/` - 部门列表
- `GET /api/auth/teams/` - 团队列表
- `GET /api/auth/roles/` - 角色列表

### 3. 系统设置模块 (System)
**路径**: `apps/system/`
**功能**: 系统配置管理和通知系统

#### 主要功能
- **系统设置**: 动态配置管理
- **系统通知**: 站内通知系统
- **邮件模板**: 邮件模板管理
- **系统监控**: 系统健康状态监控

#### 配置选项
```python
# 在 .env 文件中配置
ENABLE_SYSTEM_NOTIFICATIONS=true  # 启用系统通知
ENABLE_EMAIL_TEMPLATES=true       # 启用邮件模板
```

#### API 端点
- `GET /api/system/settings/` - 获取系统设置
- `PUT /api/system/settings/<key>/` - 更新系统设置
- `POST /api/system/settings/bulk/update/` - 批量更新设置
- `GET /api/system/notifications/` - 获取系统通知
- `GET /api/system/email-templates/` - 获取邮件模板
- `GET /api/system/stats/` - 获取系统统计
- `GET /api/system/health/` - 系统健康检查

#### 使用示例
```python
# 创建系统通知
from apps.system.signals import create_system_notification

create_system_notification(
    title="系统维护通知",
    message="系统将于今晚进行维护，预计持续2小时",
    notification_type="warning",
    priority="high",
    is_global=True
)
```

## 环境变量配置

### 基础配置
```env
# 项目基础信息
PROJECT_NAME={{PROJECT_NAME}}
SECRET_KEY={{SECRET_KEY}}
DEBUG=True

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# 功能开关
ENABLE_AUDIT_LOGGING=true
ENABLE_SYSTEM_NOTIFICATIONS=true
ENABLE_EMAIL_TEMPLATES=true
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_CACHING=false
```

### 邮件配置
```env
# SMTP 配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 安全配置
```env
# 会话配置
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_MINUTES=30

# CORS 配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
ALLOWED_HOSTS=localhost,127.0.0.1,yourproject.com
```

## 数据库迁移

### 初始化数据库
```bash
# 创建迁移文件
python manage.py makemigrations users
python manage.py makemigrations system
python manage.py makemigrations audit  # 如果启用了审计模块

# 应用迁移
python manage.py migrate
```

### 创建初始数据
```bash
# 创建超级用户
python manage.py createsuperuser

# 加载初始系统设置（如果有）
python manage.py loaddata initial_settings.json
```

## 自定义扩展

### 添加新的审计事件类型
```python
# 在 apps/audit/models.py 中的 OperationLog.ACTION_TYPES 添加新类型
('custom_action', _("Custom Action")),
```

### 添加新的系统设置
```python
# 通过 Django Admin 或 API 添加新设置
SystemSetting.objects.create(
    key="custom_setting",
    name="自定义设置",
    description="这是一个自定义设置",
    category="custom",
    value_type="string",
    value="default_value",
    default_value="default_value"
)
```

### 自定义通知类型
```python
# 在 apps/system/models.py 中的 SystemNotification.NOTIFICATION_TYPES 添加新类型
('custom', _("Custom Notification")),
```

## 性能优化建议

### 数据库优化
1. 为审计日志表添加适当的索引
2. 定期清理过期的审计日志
3. 使用数据库分区（对于大量数据）

### 缓存配置
```python
# 启用缓存以提高性能
ENABLE_CACHING=true

# Redis 配置
REDIS_URL=redis://localhost:6379/0
```

### 异步任务
```python
# 使用 Celery 处理耗时任务
# 如发送邮件、生成报告等
```

## 安全注意事项

1. **审计日志安全**: 确保审计日志不被篡改
2. **敏感数据**: 避免在日志中记录敏感信息
3. **权限控制**: 严格控制系统设置的访问权限
4. **数据备份**: 定期备份审计数据和系统配置

## 故障排除

### 常见问题

1. **审计日志不记录**
   - 检查 `ENABLE_AUDIT_LOGGING` 设置
   - 确认信号处理器已正确注册

2. **系统通知不显示**
   - 检查 `ENABLE_SYSTEM_NOTIFICATIONS` 设置
   - 确认通知接收者已正确创建

3. **邮件发送失败**
   - 检查 SMTP 配置
   - 确认邮件模板存在且有效

### 日志查看
```bash
# 查看应用日志
tail -f logs/django.log

# 查看审计日志
python manage.py shell
>>> from apps.audit.models import OperationLog
>>> OperationLog.objects.filter(user__username='admin').order_by('-created_at')[:10]
```

## 更新和维护

### 模块更新
1. 备份现有数据
2. 更新模板文件
3. 运行数据库迁移
4. 测试功能完整性

### 数据清理
```python
# 清理过期的审计日志（保留90天）
from datetime import datetime, timedelta
from apps.audit.models import OperationLog

cutoff_date = datetime.now() - timedelta(days=90)
OperationLog.objects.filter(created_at__lt=cutoff_date).delete()
```
