#!/bin/bash

# Docker启动脚本 - 用于快速启动{{PROJECT_NAME}}项目

set -e

echo "🚀 启动{{PROJECT_NAME}}项目..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 构建并启动服务
echo "📦 构建Docker镜像..."
docker-compose build

echo "🔧 启动服务..."
docker-compose up -d

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
docker-compose exec backend python manage.py migrate

# 初始化系统设置
echo "⚙️ 初始化系统设置..."
docker-compose exec backend python manage.py init_system_settings

# 创建超级用户（如果不存在）
echo "👤 检查超级用户..."
docker-compose exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('超级用户已创建: admin/admin123')
else:
    print('超级用户已存在')
"

echo "✅ 项目启动完成！"
echo ""
echo "🌐 访问地址："
echo "  前端: http://localhost:5173"
echo "  后端API: http://localhost:8001"
echo "  API文档: http://localhost:8001/api/docs/"
echo "  Django Admin: http://localhost:8001/admin/"
echo ""
echo "🔑 默认管理员账户："
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "📋 常用命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  进入后端容器: docker-compose exec backend bash"
echo "  进入前端容器: docker-compose exec frontend sh"
