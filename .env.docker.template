# Docker环境变量配置文件
# 复制此文件为.env.docker并根据需要修改配置

# 项目配置
PROJECT_NAME={{PROJECT_NAME}}
PROJECT_SLUG={{PROJECT_SLUG}}

# 数据库配置
POSTGRES_DB={{PROJECT_SLUG}}_db
POSTGRES_USER={{PROJECT_SLUG}}_user
POSTGRES_PASSWORD={{PROJECT_SLUG}}_password_123

# Django配置
DEBUG=True
SECRET_KEY={{SECRET_KEY}}
ALLOWED_HOSTS=localhost,127.0.0.1,backend
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# 数据库连接
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}

# Redis配置
REDIS_URL=redis://redis:6379/0

# 邮件配置（可选）
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# 文件存储配置
MEDIA_URL=/media/
STATIC_URL=/static/

# 安全配置
SECURE_SSL_REDIRECT=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True

# 日志配置
LOG_LEVEL=INFO

# 前端配置
VITE_API_BASE_URL=http://localhost:8001
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5173
