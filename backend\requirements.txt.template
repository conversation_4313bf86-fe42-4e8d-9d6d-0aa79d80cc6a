# {{PROJECT_NAME}} Backend Requirements
# Generated from template on {{GENERATION_DATE}}

# Core Django
Django>=4.2,<5.0
django-environ>=0.10.0

# Django REST Framework
djangorestframework>=3.14.0
djangorestframework-simplejwt>=5.2.0

# API Documentation
drf-spectacular>=0.26.0

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter

# Authentication & Authorization
django-guardian>=2.4.0

# CORS handling
django-cors-headers>=4.0.0

# Password validation
django-password-validators>=1.7.0

# Email
django-anymail>=10.0  # For email service providers

# File handling
Pillow>=9.5.0  # For image processing

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3

# Security
cryptography>=41.0.0

# Audit logging dependencies (optional)
# Uncomment if ENABLE_AUDIT_LOGGING=True
# django-simple-history>=3.3.0

# System monitoring dependencies (optional)
# Uncomment if you need system monitoring
psutil>=5.9.0  # For system resource monitoring

# MFA dependencies (optional)
# Uncomment if implementing Multi-Factor Authentication
pyotp>=2.8.0  # For TOTP
qrcode>=7.4.0  # For QR code generation

# Development dependencies (optional)
# Uncomment for development environment
# django-debug-toolbar>=4.1.0
# django-extensions>=3.2.0

# Testing dependencies (optional)
# Uncomment for testing
# pytest>=7.4.0
# pytest-django>=4.5.0
# factory-boy>=3.3.0
# coverage>=7.2.0

# Production dependencies (optional)
# Uncomment for production deployment
# gunicorn>=21.0.0
# whitenoise>=6.5.0
# sentry-sdk>=1.28.0

# Cache dependencies (optional)
# Uncomment if ENABLE_CACHING=True
# redis>=4.6.0
# django-redis>=5.3.0

# Celery for background tasks (optional)
# Uncomment if implementing background tasks
# celery>=5.3.0
# django-celery-beat>=2.5.0
# django-celery-results>=2.5.0
