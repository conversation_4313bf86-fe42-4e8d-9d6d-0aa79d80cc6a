# 后端基础镜像 - 预安装所有Python依赖
FROM python:3.12.7-bullseye

# 镜像元数据
LABEL maintainer="快速部署测试 Team"
LABEL description="快速部署测试 Backend Base Image"
LABEL version="1.0"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DJANGO_SETTINGS_MODULE=config.settings \
    PYTHONPATH=/app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制requirements文件
COPY requirements.txt /app/requirements.txt

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install -r requirements.txt && \
    pip cache purge

# 创建应用用户
RUN useradd --create-home --shell /bin/bash app

# 创建必要的目录
RUN mkdir -p /app/logs /app/media /app/static /app/staticfiles && \
    chown -R app:app /app

# 创建启动脚本
RUN echo '#!/bin/bash' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 等待数据库' >> /app/entrypoint.sh && \
    echo 'echo "等待数据库连接..."' >> /app/entrypoint.sh && \
    echo 'python manage.py check --database default' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 运行迁移' >> /app/entrypoint.sh && \
    echo 'echo "运行数据库迁移..."' >> /app/entrypoint.sh && \
    echo 'python manage.py migrate --noinput' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 收集静态文件' >> /app/entrypoint.sh && \
    echo 'echo "收集静态文件..."' >> /app/entrypoint.sh && \
    echo 'python manage.py collectstatic --noinput' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 初始化系统设置' >> /app/entrypoint.sh && \
    echo 'echo "初始化系统设置..."' >> /app/entrypoint.sh && \
    echo 'python manage.py init_system_settings || true' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 启动应用' >> /app/entrypoint.sh && \
    echo 'echo "启动Django应用..."' >> /app/entrypoint.sh && \
    echo 'exec "$@"' >> /app/entrypoint.sh

RUN chmod +x /app/entrypoint.sh && \
    chown app:app /app/entrypoint.sh

# 切换到应用用户
USER app

# 暴露端口
EXPOSE 8000

# 设置入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
