#!/usr/bin/env python3
"""
Project Template Initializer
Initializes a new project from the template with custom configuration.
"""

import os
import sys
import json
import shutil
import secrets
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any


class ProjectInitializer:
    def __init__(self, template_dir: Path, target_dir: Path, config_file: Path = None):
        self.template_dir = template_dir
        self.target_dir = target_dir
        self.config_file = config_file
        self.config = {}
        self.template_vars = {}
        
    def load_config(self):
        """Load project configuration from JSON file or interactive input."""
        if self.config_file and self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print(f"✅ Loaded configuration from {self.config_file}")
        else:
            print("📝 No configuration file provided. Using interactive mode.")
            self.config = self.interactive_config()
            
    def interactive_config(self) -> Dict[str, Any]:
        """Interactive configuration setup."""
        print("\n🚀 Project Template Initializer")
        print("=" * 50)
        
        config = {
            "project": {},
            "author": {},
            "features": {"core": {}, "optional": {}, "development": {}},
            "theme": {},
            "security": {}
        }
        
        # Basic project information
        config["project"]["name"] = input("Project Name: ").strip()
        config["project"]["title"] = input("Project Title (display name): ").strip() or config["project"]["name"]
        config["project"]["description"] = input("Project Description: ").strip()
        config["project"]["slug"] = input("Project Slug (URL-safe name): ").strip().lower().replace(' ', '-')
        
        # Author information
        config["author"]["name"] = input("Author Name: ").strip()
        config["author"]["email"] = input("Author Email: ").strip()
        
        # Basic features
        print("\n🔧 Feature Configuration")
        config["features"]["optional"]["email_notifications"] = self.ask_yes_no("Enable email notifications?", False)
        config["features"]["optional"]["audit_logging"] = self.ask_yes_no("Enable audit logging?", True)
        config["features"]["optional"]["file_upload"] = self.ask_yes_no("Enable file upload?", True)
        config["features"]["optional"]["caching"] = self.ask_yes_no("Enable Redis caching?", False)
        
        # Theme
        print("\n🎨 Theme Configuration")
        config["theme"]["primary_color"] = input("Primary color (hex): ").strip() or "#1890ff"
        config["theme"]["layout"] = input("Layout (side/top/mix): ").strip() or "side"
        
        return config
        
    def ask_yes_no(self, question: str, default: bool = False) -> bool:
        """Ask a yes/no question with default value."""
        default_str = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_str}]: ").strip().lower()
        if not response:
            return default
        return response in ['y', 'yes', 'true', '1']
        
    def generate_template_vars(self):
        """Generate template variables from configuration."""
        project = self.config.get("project", {})
        author = self.config.get("author", {})
        
        self.template_vars = {
            # Project info
            "PROJECT_NAME": project.get("name", "My Project"),
            "PROJECT_TITLE": project.get("title", project.get("name", "My Project")),
            "PROJECT_DESCRIPTION": project.get("description", "A Django + Vue.js application"),
            "PROJECT_VERSION": project.get("version", "1.0.0"),
            "PROJECT_SLUG": project.get("slug", "my-project"),
            "PROJECT_HOMEPAGE": project.get("homepage", ""),
            "PROJECT_REPOSITORY": project.get("repository", ""),
            "PROJECT_LICENSE": project.get("license", "MIT"),
            "PROJECT_LOGO": project.get("logo", ""),
            
            # Author info
            "AUTHOR_NAME": author.get("name", ""),
            "AUTHOR_EMAIL": author.get("email", ""),
            "AUTHOR_URL": author.get("url", ""),
            
            # Generated values
            "SECRET_KEY": secrets.token_urlsafe(50),
            "GENERATION_DATE": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            
            # Default values
            "MODULE_NAME": "core",  # Default module name
            "DOMAIN": "localhost",
            "DEBUG": "True",
            "API_BASE_URL": "http://localhost:8000",
            "API_VERSION": "v1",
            "API_TIMEOUT": "30000",
            "API_TITLE": f"{project.get('name', 'My Project')} API",
            "API_DESCRIPTION": project.get("description", "API Documentation"),
            
            # Theme
            "THEME_PRIMARY_COLOR": self.config.get("theme", {}).get("primary_color", "#1890ff"),
            "THEME_LAYOUT": self.config.get("theme", {}).get("layout", "side"),
            "THEME_DARK_MODE": "false",
            "THEME_COMPACT_MODE": "false",
            
            # Feature flags
            "ENABLE_API_DOCS": "true",
            "ENABLE_ADMIN_INTERFACE": "true",
            "ENABLE_DEBUG_TOOLBAR": "true",
            "ENABLE_CORS": "true",
            "ENABLE_EMAIL_NOTIFICATIONS": str(self.config.get("features", {}).get("optional", {}).get("email_notifications", False)).lower(),
            "ENABLE_AUDIT_LOGGING": str(self.config.get("features", {}).get("optional", {}).get("audit_logging", True)).lower(),
            "ENABLE_CACHING": str(self.config.get("features", {}).get("optional", {}).get("caching", False)).lower(),
            
            # Security
            "ALLOWED_HOSTS": "localhost,127.0.0.1",
            "CORS_ALLOWED_ORIGINS": "http://localhost:5173,http://localhost:3000",
            
            # Development
            "DEV_SERVER_HOST": "0.0.0.0",
            "DEV_SERVER_PORT": "5173",
            "DEV_SERVER_OPEN": "false",
            "DEVELOPMENT_SERVER_HOST": "0.0.0.0",
            "DEVELOPMENT_SERVER_PORT": "8000",
            
            # Build settings
            "BUILD_SOURCEMAP": "false",
            "BUILD_ANALYZE": "false",
            "BUILD_COMPRESS": "true",
            
            # Database
            "DB_USER": "root",
            "DB_PASSWORD": "password",
            
            # Email
            "EMAIL_HOST": "smtp.gmail.com",
            "EMAIL_PORT": "587",
            "EMAIL_USE_TLS": "true",
            "EMAIL_HOST_USER": "",
            "EMAIL_HOST_PASSWORD": "",
            "DEFAULT_FROM_EMAIL": f"noreply@{project.get('slug', 'localhost')}",
            
            # Redis
            "REDIS_URL": "redis://localhost:6379/1",
            "CACHE_TTL": "300",
            
            # Celery
            "CELERY_BROKER_URL": "redis://localhost:6379/0",
            "CELERY_RESULT_BACKEND": "redis://localhost:6379/0",
            
            # File storage
            "MEDIA_ROOT": "media",
            "STATIC_ROOT": "staticfiles",
            "MAX_UPLOAD_SIZE": "10485760",  # 10MB
            "MAX_FILE_SIZE": "10MB",
            "ALLOWED_FILE_TYPES": "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx",
            
            # Logging
            "LOG_LEVEL": "INFO",
            "LOG_FILE": "logs/app.log",
            
            # Localization
            "DEFAULT_LOCALE": "en",
            "FALLBACK_LOCALE": "en",
            "AVAILABLE_LOCALES": "en,zh-CN",
            
            # Security
            "ENABLE_HTTPS": "false",
            "CSRF_TOKEN_NAME": "csrftoken",
            
            # Third-party
            "GOOGLE_ANALYTICS_ID": "",
            "SENTRY_DSN": "",
            
            # Frontend features
            "ENABLE_PWA": "false",
            "ENABLE_MOCK": "false",
            "ENABLE_DEVTOOLS": "true",
            "ENABLE_I18N": "false",
            "ENABLE_ANALYTICS": "false",
            "ENABLE_USER_MANAGEMENT": "true",
            "ENABLE_AUDIT_LOG": str(self.config.get("features", {}).get("optional", {}).get("audit_logging", True)).lower(),
            "ENABLE_NOTIFICATIONS": "false",
            "ENABLE_FILE_UPLOAD": str(self.config.get("features", {}).get("optional", {}).get("file_upload", True)).lower(),
            "ENABLE_EXPORT": "true",
        }
        
    def replace_template_vars(self, content: str) -> str:
        """Replace template variables in content."""
        for var, value in self.template_vars.items():
            content = content.replace(f"{{{{{var}}}}}", str(value))
        return content
        
    def copy_and_process_file(self, src_path: Path, dst_path: Path):
        """Copy and process a single file."""
        # Read source file
        try:
            with open(src_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Binary file, copy as-is
            shutil.copy2(src_path, dst_path)
            return
            
        # Process template variables
        processed_content = self.replace_template_vars(content)
        
        # Write to destination
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        with open(dst_path, 'w', encoding='utf-8') as f:
            f.write(processed_content)
            
    def process_filename(self, filename: str) -> str:
        """Process template variables in filename."""
        # Remove .template extension and process variables
        if filename.endswith('.template'):
            filename = filename[:-9]  # Remove .template
        return self.replace_template_vars(filename)
        
    def copy_template_files(self):
        """Copy and process all template files."""
        print("\n📁 Copying template files...")
        
        for root, dirs, files in os.walk(self.template_dir):
            # Skip scripts directory
            if 'scripts' in Path(root).parts:
                continue
                
            rel_path = Path(root).relative_to(self.template_dir)
            dst_dir = self.target_dir / rel_path
            
            for file in files:
                src_file = Path(root) / file
                dst_file = dst_dir / self.process_filename(file)
                
                print(f"  📄 {src_file.relative_to(self.template_dir)} -> {dst_file.relative_to(self.target_dir)}")
                self.copy_and_process_file(src_file, dst_file)
                
    def create_requirements_txt(self):
        """Create requirements.txt from template."""
        template_file = self.template_dir / "requirements.txt.template"
        target_file = self.target_dir / "backend" / "requirements.txt"
        
        if template_file.exists():
            print("📦 Creating requirements.txt...")
            self.copy_and_process_file(template_file, target_file)
            
    def create_virtual_environment(self):
        """Create Python virtual environment."""
        venv_path = self.target_dir / "backend" / "venv"
        if not venv_path.exists():
            print("🐍 Creating Python virtual environment...")
            os.system(f"python -m venv {venv_path}")
            
    def install_dependencies(self):
        """Install Python and Node.js dependencies."""
        print("📦 Installing dependencies...")
        
        # Python dependencies
        backend_dir = self.target_dir / "backend"
        if (backend_dir / "requirements.txt").exists():
            print("  🐍 Installing Python dependencies...")
            if os.name == 'nt':  # Windows
                activate_script = backend_dir / "venv" / "Scripts" / "activate.bat"
                os.system(f"cd {backend_dir} && {activate_script} && pip install -r requirements.txt")
            else:  # Unix-like
                os.system(f"cd {backend_dir} && source venv/bin/activate && pip install -r requirements.txt")
                
        # Node.js dependencies
        frontend_dir = self.target_dir / "frontend"
        if (frontend_dir / "package.json").exists():
            print("  📦 Installing Node.js dependencies...")
            os.system(f"cd {frontend_dir} && npm install")
            
    def initialize_database(self):
        """Initialize Django database."""
        backend_dir = self.target_dir / "backend"
        manage_py = backend_dir / "manage.py"
        
        if manage_py.exists():
            print("🗄️ Initializing database...")
            if os.name == 'nt':  # Windows
                activate_script = backend_dir / "venv" / "Scripts" / "activate.bat"
                os.system(f"cd {backend_dir} && {activate_script} && python manage.py migrate")
            else:  # Unix-like
                os.system(f"cd {backend_dir} && source venv/bin/activate && python manage.py migrate")
                
    def create_startup_scripts(self):
        """Create startup scripts for the project."""
        print("🚀 Creating startup scripts...")
        
        # Backend startup script
        if os.name == 'nt':  # Windows
            backend_script = self.target_dir / "start_backend.bat"
            with open(backend_script, 'w') as f:
                f.write(f"""@echo off
echo Starting {self.template_vars['PROJECT_NAME']} Backend...
cd backend
call venv\\Scripts\\activate.bat
python manage.py runserver 0.0.0.0:8000
pause
""")
            
            # Frontend startup script
            frontend_script = self.target_dir / "start_frontend.bat"
            with open(frontend_script, 'w') as f:
                f.write(f"""@echo off
echo Starting {self.template_vars['PROJECT_NAME']} Frontend...
cd frontend
npm run dev
pause
""")
        else:  # Unix-like
            backend_script = self.target_dir / "start_backend.sh"
            with open(backend_script, 'w') as f:
                f.write(f"""#!/bin/bash
echo "Starting {self.template_vars['PROJECT_NAME']} Backend..."
cd backend
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
""")
            os.chmod(backend_script, 0o755)
            
            frontend_script = self.target_dir / "start_frontend.sh"
            with open(frontend_script, 'w') as f:
                f.write(f"""#!/bin/bash
echo "Starting {self.template_vars['PROJECT_NAME']} Frontend..."
cd frontend
npm run dev
""")
            os.chmod(frontend_script, 0o755)
            
    def create_readme(self):
        """Create project README file."""
        readme_content = f"""# {self.template_vars['PROJECT_NAME']}

{self.template_vars['PROJECT_DESCRIPTION']}

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone {self.template_vars['PROJECT_REPOSITORY']}
   cd {self.template_vars['PROJECT_SLUG']}
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   # Windows
   .\\venv\\Scripts\\activate
   # Linux/Mac
   source venv/bin/activate
   
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py runserver 0.0.0.0:8000
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

### Quick Start Scripts

- **Windows**: Double-click `start_backend.bat` and `start_frontend.bat`
- **Linux/Mac**: Run `./start_backend.sh` and `./start_frontend.sh`

## 📚 Documentation

- **Backend API**: http://localhost:8000/api/docs/
- **Frontend**: http://localhost:5173

## 🔧 Configuration

Copy `.env.template` files to `.env` and update the values:
- `backend/.env` - Backend configuration
- `frontend/.env.local` - Frontend configuration

## 🎯 Features

- ✅ User Authentication & Authorization
- ✅ RESTful API with Django REST Framework
- ✅ Modern Vue.js Frontend with Ant Design
- ✅ API Documentation with Swagger
- ✅ Database Migrations
- ✅ Development Tools Integration

## 📄 License

This project is licensed under the {self.template_vars['PROJECT_LICENSE']} License.

## 👥 Author

{self.template_vars['AUTHOR_NAME']} - {self.template_vars['AUTHOR_EMAIL']}

---

Generated from template on {self.template_vars['GENERATION_DATE']}
"""
        
        readme_file = self.target_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
            
    def initialize(self):
        """Initialize the project."""
        print(f"🎯 Initializing project: {self.config.get('project', {}).get('name', 'Unknown')}")
        print(f"📁 Target directory: {self.target_dir}")
        
        # Create target directory
        self.target_dir.mkdir(parents=True, exist_ok=True)
        
        # Load configuration and generate variables
        self.load_config()
        self.generate_template_vars()
        
        # Copy and process template files
        self.copy_template_files()
        self.create_requirements_txt()
        
        # Setup development environment
        self.create_virtual_environment()
        self.install_dependencies()
        self.initialize_database()
        
        # Create additional files
        self.create_startup_scripts()
        self.create_readme()
        
        print("\n✅ Project initialization completed!")
        print(f"📁 Project created in: {self.target_dir}")
        print("\n🚀 Next steps:")
        print("1. Review and update .env files")
        print("2. Start the backend and frontend servers")
        print("3. Open http://localhost:5173 in your browser")


def main():
    parser = argparse.ArgumentParser(description="Initialize a new project from template")
    parser.add_argument("project_name", help="Name of the new project")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--target", "-t", help="Target directory (default: current directory)")
    parser.add_argument("--template", help="Template directory path")
    
    args = parser.parse_args()
    
    # Determine paths
    current_dir = Path.cwd()
    template_dir = Path(args.template) if args.template else current_dir
    target_dir = Path(args.target) / args.project_name if args.target else current_dir / args.project_name
    config_file = Path(args.config) if args.config else None
    
    # Initialize project
    initializer = ProjectInitializer(template_dir, target_dir, config_file)
    initializer.initialize()


if __name__ == "__main__":
    main()
