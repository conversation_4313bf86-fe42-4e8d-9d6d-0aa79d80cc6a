"""
Audit signals for 快速部署测试
Automatic audit logging for various system events
"""

from django.db.models.signals import post_save, post_delete, pre_save
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.sessions.models import Session
import logging

from .models import OperationLog, LoginLog, SecurityEvent
from .utils import get_client_ip, get_user_agent, detect_suspicious_activity

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login"""
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        # Create login log
        login_log = LoginLog.objects.create(
            user=user,
            username=user.username,
            login_type='password',
            result='success',
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=request.session.session_key,
        )
        
        # Create operation log
        OperationLog.objects.create(
            user=user,
            action_type='user_login',
            result='success',
            description=f"User {user.username} logged in successfully",
            ip_address=ip_address,
            user_agent=user_agent,
            request_method='POST',
            request_path=request.path,
        )
        
        # Check for suspicious activity
        if detect_suspicious_activity(user, ip_address):
            SecurityEvent.objects.create(
                event_type='suspicious_login',
                severity='medium',
                title=f"Suspicious login detected for user {user.username}",
                description=f"User {user.username} logged in from unusual location or device",
                user=user,
                event_data={
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'login_time': timezone.now().isoformat(),
                }
            )
            
    except Exception as e:
        logger.error(f"Error logging user login: {e}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout"""
    try:
        if user and hasattr(user, 'username'):
            ip_address = get_client_ip(request)
            user_agent = get_user_agent(request)
            
            # Update login log with logout time
            if hasattr(request, 'session') and request.session.session_key:
                try:
                    login_log = LoginLog.objects.filter(
                        user=user,
                        session_key=request.session.session_key,
                        logout_time__isnull=True
                    ).first()
                    if login_log:
                        login_log.logout_time = timezone.now()
                        login_log.save()
                except LoginLog.DoesNotExist:
                    pass
            
            # Create operation log
            OperationLog.objects.create(
                user=user,
                action_type='user_logout',
                result='success',
                description=f"User {user.username} logged out",
                ip_address=ip_address,
                user_agent=user_agent,
                request_method='POST',
                request_path=request.path if request else '',
            )
            
    except Exception as e:
        logger.error(f"Error logging user logout: {e}")


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts"""
    try:
        username = credentials.get('username', '')
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        # Try to get user object
        user = None
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            pass
        
        # Create login log
        LoginLog.objects.create(
            user=user,
            username=username,
            login_type='password',
            result='failed_password',
            ip_address=ip_address,
            user_agent=user_agent,
            failure_reason='Invalid username or password',
        )
        
        # Create operation log
        OperationLog.objects.create(
            user=user,
            action_type='user_login',
            result='failed',
            description=f"Failed login attempt for username: {username}",
            ip_address=ip_address,
            user_agent=user_agent,
            request_method='POST',
            request_path=request.path,
        )
        
        # Check for multiple failed attempts
        recent_failures = LoginLog.objects.filter(
            username=username,
            result__startswith='failed',
            created_at__gte=timezone.now() - timezone.timedelta(minutes=15)
        ).count()
        
        if recent_failures >= 5:
            SecurityEvent.objects.create(
                event_type='multiple_failed_logins',
                severity='high',
                title=f"Multiple failed login attempts for {username}",
                description=f"User {username} has {recent_failures} failed login attempts in the last 15 minutes",
                user=user,
                event_data={
                    'username': username,
                    'ip_address': ip_address,
                    'failure_count': recent_failures,
                    'time_window': '15 minutes',
                }
            )
            
    except Exception as e:
        logger.error(f"Error logging failed login: {e}")


def log_model_change(sender, instance, action_type, user=None, request=None):
    """Generic function to log model changes"""
    try:
        if not user or not hasattr(user, 'username'):
            return
            
        model_name = sender.__name__.lower()
        object_id = str(instance.pk) if hasattr(instance, 'pk') else ''
        object_name = str(instance) if hasattr(instance, '__str__') else f"{model_name}:{object_id}"
        
        ip_address = get_client_ip(request) if request else '127.0.0.1'
        user_agent = get_user_agent(request) if request else ''
        
        OperationLog.objects.create(
            user=user,
            action_type=f"{model_name}_{action_type}",
            result='success',
            description=f"{action_type.title()} {model_name}: {object_name}",
            target_type=model_name,
            target_id=object_id,
            target_name=object_name,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request.method if request else '',
            request_path=request.path if request else '',
        )
        
    except Exception as e:
        logger.error(f"Error logging model change: {e}")


# Custom signal for manual audit logging
def log_custom_action(user, action_type, description, target_type=None, target_id=None, 
                     target_name=None, request=None, result='success', extra_data=None):
    """Log custom user actions"""
    try:
        ip_address = get_client_ip(request) if request else '127.0.0.1'
        user_agent = get_user_agent(request) if request else ''
        
        OperationLog.objects.create(
            user=user,
            action_type=action_type,
            result=result,
            description=description,
            target_type=target_type or '',
            target_id=target_id or '',
            target_name=target_name or '',
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request.method if request else '',
            request_path=request.path if request else '',
            extra_data=extra_data or {},
        )
        
    except Exception as e:
        logger.error(f"Error logging custom action: {e}")


# Example usage for business objects (customize based on your models)
# @receiver(post_save, sender=YourModel)
# def log_your_model_save(sender, instance, created, **kwargs):
#     """Log YourModel creation/update"""
#     action_type = 'create' if created else 'update'
#     # You'll need to get the user from the request context
#     # This can be done using middleware or thread-local storage
#     user = getattr(instance, '_audit_user', None)
#     request = getattr(instance, '_audit_request', None)
#     
#     if user:
#         log_model_change(sender, instance, action_type, user, request)


# @receiver(post_delete, sender=YourModel)
# def log_your_model_delete(sender, instance, **kwargs):
#     """Log YourModel deletion"""
#     user = getattr(instance, '_audit_user', None)
#     request = getattr(instance, '_audit_request', None)
#     
#     if user:
#         log_model_change(sender, instance, 'delete', user, request)
