version: '3.8'

# 简化测试配置 - 验证基础镜像功能

services:
  # 后端测试 - 验证依赖安装
  backend-test:
    image: locker-template-backend-base:v3
    entrypoint: ""
    command: >
      sh -c "
        echo '测试开始: 验证Python依赖安装' &&
        python -c 'import django; print(f\"Django版本: {django.get_version()}\")' &&
        python -c 'import rest_framework; print(\"DRF已安装\")' &&
        python -c 'import redis; print(\"Redis客户端已安装\")' &&
        python -c 'import MySQLdb; print(\"MySQL客户端已安装\")' &&
        python -c 'import requests; print(\"Requests已安装\")' &&
        echo '✅ 所有核心依赖验证成功!' &&
        echo '🚀 基础镜像功能正常' &&
        sleep 10
      "

  # 前端测试 - 验证依赖安装
  frontend-test:
    image: locker-template-frontend-base:latest
    entrypoint: ""
    command: >
      sh -c "
        echo '测试开始: 验证Node.js依赖安装' &&
        node --version &&
        npm --version &&
        npm list vue --depth=0 &&
        npm list axios --depth=0 &&
        echo '✅ 所有核心依赖验证成功!' &&
        echo '🚀 前端基础镜像功能正常' &&
        sleep 10
      "
