"""
System views for 快速部署测试
Based on the existing system management functionality
"""

from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Count, Q
from django.conf import settings
from .models import SystemSetting, EmailTemplate, SystemNotification, NotificationRecipient
from .serializers import (
    SystemSettingSerializer,
    SystemSettingUpdateSerializer,
    EmailTemplateSerializer,
    SystemNotificationSerializer,
    NotificationRecipientSerializer,
    UserNotificationSerializer,
    SystemStatsSerializer,
    SystemHealthSerializer,
    BulkSettingsUpdateSerializer,
)
import logging
import psutil
import os

User = get_user_model()
logger = logging.getLogger(__name__)


class SystemSettingListView(generics.ListAPIView):
    """System settings list view"""

    serializer_class = SystemSettingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["key", "name", "description"]
    ordering_fields = ["key", "name", "category", "created_at"]
    ordering = ["category", "order", "name"]

    def get_queryset(self):
        queryset = SystemSetting.objects.all()
        
        # Filter by category
        category = self.request.query_params.get("category")
        if category:
            queryset = queryset.filter(category=category)
        
        # Filter by public settings for non-staff users
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_public=True)
        
        return queryset


class SystemSettingDetailView(generics.RetrieveUpdateAPIView):
    """System setting detail view"""

    queryset = SystemSetting.objects.all()
    serializer_class = SystemSettingSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = "key"

    def get_object(self):
        obj = super().get_object()
        
        # Check permissions for non-public settings
        if not obj.is_public and not self.request.user.is_staff:
            from django.core.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to access this setting")
        
        return obj

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # Check if setting is readonly
        if instance.is_readonly:
            return Response(
                {"error": "This setting is read-only"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check permissions for updates
        if not request.user.is_staff:
            return Response(
                {"error": "You don't have permission to update settings"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        return super().update(request, *args, **kwargs)


class BulkSettingsUpdateView(APIView):
    """Bulk settings update view"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        if not request.user.is_staff:
            return Response(
                {"error": "You don't have permission to update settings"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = BulkSettingsUpdateSerializer(data=request.data)
        if serializer.is_valid():
            updated_settings = []
            
            for setting_data in serializer.validated_data["settings"]:
                key = setting_data["key"]
                value = setting_data["value"]
                
                try:
                    setting = SystemSetting.objects.get(key=key)
                    setting.value = value
                    setting.modified_by = request.user
                    setting.save()
                    updated_settings.append(key)
                    
                    # Log setting change (if audit module is enabled)
                    try:
                        from apps.audit.models import OperationLog
                        OperationLog.objects.create(
                            user=request.user,
                            action_type="system_setting_update",
                            target_type="system_setting",
                            target_id=key,
                            target_name=setting.name,
                            description=f"Updated setting '{key}' to '{value}'",
                            ip_address=self.get_client_ip(request),
                            user_agent=request.META.get("HTTP_USER_AGENT", ""),
                        )
                    except ImportError:
                        pass
                    except Exception as e:
                        logger.error(f"Failed to log setting change: {e}")
                        
                except SystemSetting.DoesNotExist:
                    continue
            
            return Response({
                "message": f"Updated {len(updated_settings)} settings",
                "updated_settings": updated_settings
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class EmailTemplateListCreateView(generics.ListCreateAPIView):
    """Email template list and create view"""

    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "subject", "template_type"]
    ordering_fields = ["name", "template_type", "created_at"]
    ordering = ["template_type", "name"]

    def get_queryset(self):
        queryset = EmailTemplate.objects.all()
        
        # Filter by template type
        template_type = self.request.query_params.get("template_type")
        if template_type:
            queryset = queryset.filter(template_type=template_type)
        
        # Filter by active status
        is_active = self.request.query_params.get("is_active")
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == "true")
        
        return queryset


class EmailTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Email template detail view"""

    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]


class SystemNotificationListCreateView(generics.ListCreateAPIView):
    """System notification list and create view"""

    queryset = SystemNotification.objects.all()
    serializer_class = SystemNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["title", "message"]
    ordering_fields = ["title", "priority", "created_at"]
    ordering = ["-created_at"]

    def get_queryset(self):
        queryset = SystemNotification.objects.all()
        
        # Filter by notification type
        notification_type = self.request.query_params.get("notification_type")
        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)
        
        # Filter by priority
        priority = self.request.query_params.get("priority")
        if priority:
            queryset = queryset.filter(priority=priority)
        
        # Filter by global notifications
        is_global = self.request.query_params.get("is_global")
        if is_global is not None:
            queryset = queryset.filter(is_global=is_global.lower() == "true")
        
        return queryset


class SystemNotificationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """System notification detail view"""

    queryset = SystemNotification.objects.all()
    serializer_class = SystemNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]


class UserNotificationListView(generics.ListAPIView):
    """Current user's notifications list view"""

    serializer_class = UserNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ["-notification__created_at"]

    def get_queryset(self):
        return NotificationRecipient.objects.filter(
            user=self.request.user
        ).select_related("notification")


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read(request, notification_id):
    """Mark notification as read"""
    try:
        recipient = NotificationRecipient.objects.get(
            notification_id=notification_id,
            user=request.user
        )
        recipient.mark_as_read()
        return Response({"message": "Notification marked as read"})
    except NotificationRecipient.DoesNotExist:
        return Response(
            {"error": "Notification not found"},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def dismiss_notification(request, notification_id):
    """Dismiss notification"""
    try:
        recipient = NotificationRecipient.objects.get(
            notification_id=notification_id,
            user=request.user
        )
        recipient.dismiss()
        return Response({"message": "Notification dismissed"})
    except NotificationRecipient.DoesNotExist:
        return Response(
            {"error": "Notification not found"},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def system_stats(request):
    """Get system statistics"""
    if not request.user.is_staff:
        return Response(
            {"error": "You don't have permission to view system statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    # Basic counts
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    total_settings = SystemSetting.objects.count()
    total_notifications = SystemNotification.objects.count()
    unread_notifications = NotificationRecipient.objects.filter(is_read=False).count()

    # System health
    system_status = "healthy"  # Implement your health check logic
    database_status = "connected"  # Check database connection
    cache_status = "available"  # Check cache availability

    # Resource usage
    memory_usage = {
        "total": psutil.virtual_memory().total,
        "available": psutil.virtual_memory().available,
        "percent": psutil.virtual_memory().percent,
    }
    
    disk_usage = {
        "total": psutil.disk_usage("/").total,
        "free": psutil.disk_usage("/").free,
        "percent": psutil.disk_usage("/").percent,
    }

    # Recent activity (implement based on your audit logs)
    recent_logins = []
    recent_settings_changes = []

    stats_data = {
        "total_users": total_users,
        "active_users": active_users,
        "total_settings": total_settings,
        "total_notifications": total_notifications,
        "unread_notifications": unread_notifications,
        "system_status": system_status,
        "database_status": database_status,
        "cache_status": cache_status,
        "memory_usage": memory_usage,
        "disk_usage": disk_usage,
        "recent_logins": recent_logins,
        "recent_settings_changes": recent_settings_changes,
    }

    serializer = SystemStatsSerializer(stats_data)
    return Response(serializer.data)


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def system_health(request):
    """Get system health status"""
    if not request.user.is_staff:
        return Response(
            {"error": "You don't have permission to view system health"},
            status=status.HTTP_403_FORBIDDEN
        )

    # Implement your health check logic here
    health_data = {
        "status": "healthy",
        "timestamp": timezone.now(),
        "database": {"status": "connected", "response_time": 0.05},
        "cache": {"status": "available", "response_time": 0.01},
        "storage": {"status": "available", "free_space": "85%"},
        "response_time": 0.1,
        "memory_usage_percent": psutil.virtual_memory().percent,
        "cpu_usage_percent": psutil.cpu_percent(),
        "disk_usage_percent": psutil.disk_usage("/").percent,
    }

    serializer = SystemHealthSerializer(health_data)
    return Response(serializer.data)
