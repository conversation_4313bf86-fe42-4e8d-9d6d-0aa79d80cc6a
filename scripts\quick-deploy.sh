#!/bin/bash

# 快速部署脚本 - 使用预构建的基础镜像
# 适用于内网环境，无需重新构建镜像

set -e

echo "🚀 {{PROJECT_NAME}} 快速部署脚本"
echo "使用预构建基础镜像，适合内网环境"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查基础镜像是否存在
BACKEND_IMAGE="locker-template-backend-base:latest"
FRONTEND_IMAGE="locker-template-frontend-base:latest"

echo "🔍 检查基础镜像..."
if ! docker image inspect $BACKEND_IMAGE > /dev/null 2>&1; then
    echo "❌ 后端基础镜像不存在: $BACKEND_IMAGE"
    echo "请先运行构建脚本: ./scripts/build-images.sh"
    echo "或导入预构建的镜像文件"
    exit 1
fi

if ! docker image inspect $FRONTEND_IMAGE > /dev/null 2>&1; then
    echo "❌ 前端基础镜像不存在: $FRONTEND_IMAGE"
    echo "请先运行构建脚本: ./scripts/build-images.sh"
    echo "或导入预构建的镜像文件"
    exit 1
fi

echo "✅ 基础镜像检查通过"

# 创建环境变量文件（如果不存在）
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.docker.template .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 使用基础镜像启动服务
echo "🔧 启动服务（使用基础镜像）..."
docker-compose -f docker-compose.base.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
if docker-compose -f docker-compose.base.yml ps | grep -q "Up"; then
    echo "✅ 服务启动成功"
else
    echo "❌ 服务启动失败，查看日志："
    docker-compose -f docker-compose.base.yml logs
    exit 1
fi

# 等待数据库就绪
echo "⏳ 等待数据库就绪..."
timeout 60 bash -c 'until docker-compose -f docker-compose.base.yml exec -T db pg_isready -U {{PROJECT_SLUG}}_user; do sleep 2; done'

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
docker-compose -f docker-compose.base.yml exec -T backend python manage.py migrate

# 初始化系统设置
echo "⚙️ 初始化系统设置..."
docker-compose -f docker-compose.base.yml exec -T backend python manage.py init_system_settings

# 创建超级用户（如果不存在）
echo "👤 检查超级用户..."
docker-compose -f docker-compose.base.yml exec -T backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('超级用户已创建: admin/admin123')
else:
    print('超级用户已存在')
"

# 收集静态文件
echo "📁 收集静态文件..."
docker-compose -f docker-compose.base.yml exec -T backend python manage.py collectstatic --noinput

echo ""
echo "🎉 部署完成！"
echo ""
echo "🌐 访问地址："
echo "  前端应用: http://localhost:5173"
echo "  后端API: http://localhost:8001"
echo "  API文档: http://localhost:8001/api/docs/"
echo "  Django Admin: http://localhost:8001/admin/"
echo ""
echo "🔑 默认管理员账户："
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "📋 管理命令："
echo "  查看状态: docker-compose -f docker-compose.base.yml ps"
echo "  查看日志: docker-compose -f docker-compose.base.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.base.yml down"
echo "  重启服务: docker-compose -f docker-compose.base.yml restart"
echo ""
echo "💡 提示："
echo "  - 首次启动可能需要几分钟时间"
echo "  - 如遇问题，请查看日志文件"
echo "  - 生产环境请修改默认密码"
