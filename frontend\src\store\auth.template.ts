import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginCredentials, RegisterData } from '@/types/auth'
import { authApi } from '@/api/auth'
import { message } from 'ant-design-vue'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const loading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_staff || user.value?.is_superuser || false)
  const userDisplayName = computed(() => {
    if (!user.value) return ''
    return user.value.display_name || user.value.full_name || user.value.email || user.value.username
  })

  // Actions
  const setTokens = (access: string, refresh: string) => {
    accessToken.value = access
    refreshToken.value = refresh
    localStorage.setItem('access_token', access)
    localStorage.setItem('refresh_token', refresh)
  }

  const clearTokens = () => {
    accessToken.value = null
    refreshToken.value = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  const setUser = (userData: User) => {
    user.value = userData
  }

  const clearUser = () => {
    user.value = null
  }

  const login = async (credentials: LoginCredentials) => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      setTokens(response.access, response.refresh)
      setUser(response.user)
      
      message.success('Login successful')
      return response
    } catch (error: any) {
      message.error(error.message || 'Login failed')
      throw error
    } finally {
      loading.value = false
    }
  }

  const register = async (data: RegisterData) => {
    try {
      loading.value = true
      const response = await authApi.register(data)
      
      setTokens(response.access, response.refresh)
      setUser(response.user)
      
      message.success('Registration successful')
      return response
    } catch (error: any) {
      message.error(error.message || 'Registration failed')
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      if (refreshToken.value) {
        await authApi.logout(refreshToken.value)
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearTokens()
      clearUser()
      message.success('Logged out successfully')
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      accessToken.value = response.access
      localStorage.setItem('access_token', response.access)
      
      return response.access
    } catch (error) {
      // Refresh token is invalid, logout user
      await logout()
      throw error
    }
  }

  const getCurrentUser = async () => {
    try {
      const userData = await authApi.getCurrentUser()
      setUser(userData)
      return userData
    } catch (error) {
      console.error('Get current user error:', error)
      throw error
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      loading.value = true
      const updatedUser = await authApi.updateProfile(profileData)
      setUser(updatedUser)
      message.success('Profile updated successfully')
      return updatedUser
    } catch (error: any) {
      message.error(error.message || 'Profile update failed')
      throw error
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData: { old_password: string; new_password: string; new_password_confirm: string }) => {
    try {
      loading.value = true
      await authApi.changePassword(passwordData)
      message.success('Password changed successfully')
    } catch (error: any) {
      message.error(error.message || 'Password change failed')
      throw error
    } finally {
      loading.value = false
    }
  }

  const initializeAuth = async () => {
    const storedAccessToken = localStorage.getItem('access_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    
    if (storedAccessToken && storedRefreshToken) {
      accessToken.value = storedAccessToken
      refreshToken.value = storedRefreshToken
      
      try {
        await getCurrentUser()
      } catch (error) {
        // Token might be expired, try to refresh
        try {
          await refreshAccessToken()
          await getCurrentUser()
        } catch (refreshError) {
          // Both tokens are invalid, clear everything
          clearTokens()
          clearUser()
        }
      }
    }
  }

  const restoreAuth = async (): Promise<boolean> => {
    try {
      await initializeAuth()
      return isAuthenticated.value
    } catch (error) {
      return false
    }
  }

  return {
    // State
    user,
    accessToken,
    refreshToken,
    loading,
    
    // Getters
    isAuthenticated,
    isAdmin,
    userDisplayName,
    
    // Actions
    login,
    register,
    logout,
    refreshAccessToken,
    getCurrentUser,
    updateProfile,
    changePassword,
    initializeAuth,
    restoreAuth,
    setTokens,
    clearTokens,
    setUser,
    clearUser
  }
}, {
  persist: {
    key: '{{PROJECT_SLUG}}-auth',
    storage: localStorage,
    paths: ['user']
  }
})
