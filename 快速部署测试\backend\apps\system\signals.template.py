"""
System signals for 快速部署测试
Automatic system event handling and notifications
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
import logging

from .models import SystemSetting, SystemNotification, NotificationRecipient

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=SystemSetting)
def log_setting_change(sender, instance, created, **kwargs):
    """Log system setting changes"""
    try:
        # Log setting change (if audit module is enabled)
        try:
            from apps.audit.models import OperationLog
            
            action_type = "system_setting_create" if created else "system_setting_update"
            description = f"{'Created' if created else 'Updated'} system setting: {instance.name}"
            
            OperationLog.objects.create(
                user=instance.modified_by,
                action_type=action_type,
                target_type="system_setting",
                target_id=instance.key,
                target_name=instance.name,
                description=description,
                extra_data={
                    "key": instance.key,
                    "value": instance.value,
                    "category": instance.category,
                    "value_type": instance.value_type,
                }
            )
        except ImportError:
            # Audit module not available
            pass
        except Exception as e:
            logger.error(f"Failed to log setting change: {e}")
            
        # Create notification for critical setting changes
        if instance.requires_restart and not created:
            try:
                SystemNotification.objects.create(
                    title="System Restart Required",
                    message=f"Setting '{instance.name}' has been changed and requires a system restart to take effect.",
                    notification_type="warning",
                    priority="high",
                    is_global=True,
                    created_by=instance.modified_by,
                )
            except Exception as e:
                logger.error(f"Failed to create restart notification: {e}")
                
    except Exception as e:
        logger.error(f"Error in setting change signal: {e}")


@receiver(post_save, sender=SystemNotification)
def create_notification_recipients(sender, instance, created, **kwargs):
    """Create notification recipients when a notification is created"""
    if created:
        try:
            if instance.is_global:
                # Create recipients for all active users
                active_users = User.objects.filter(is_active=True)
                recipients = [
                    NotificationRecipient(notification=instance, user=user)
                    for user in active_users
                ]
                NotificationRecipient.objects.bulk_create(recipients, ignore_conflicts=True)
            else:
                # Recipients will be added manually through the target_users relationship
                pass
                
        except Exception as e:
            logger.error(f"Failed to create notification recipients: {e}")


@receiver(post_save, sender=User)
def create_global_notifications_for_new_user(sender, instance, created, **kwargs):
    """Create notification recipients for new users for existing global notifications"""
    if created:
        try:
            # Get all active global notifications
            global_notifications = SystemNotification.objects.filter(
                is_global=True,
                expires_at__isnull=True
            ).union(
                SystemNotification.objects.filter(
                    is_global=True,
                    expires_at__gt=timezone.now()
                )
            )
            
            # Create recipients for the new user
            recipients = [
                NotificationRecipient(notification=notification, user=instance)
                for notification in global_notifications
            ]
            NotificationRecipient.objects.bulk_create(recipients, ignore_conflicts=True)
            
        except Exception as e:
            logger.error(f"Failed to create global notifications for new user: {e}")


def create_system_notification(title, message, notification_type="info", priority="normal", 
                             target_users=None, is_global=False, created_by=None):
    """Helper function to create system notifications"""
    try:
        notification = SystemNotification.objects.create(
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            is_global=is_global,
            created_by=created_by,
        )
        
        if target_users and not is_global:
            # Add specific target users
            notification.target_users.set(target_users)
        
        return notification
        
    except Exception as e:
        logger.error(f"Failed to create system notification: {e}")
        return None


def notify_admins(title, message, notification_type="warning", priority="high"):
    """Helper function to notify all admin users"""
    try:
        admin_users = User.objects.filter(is_staff=True, is_active=True)
        if admin_users.exists():
            notification = create_system_notification(
                title=title,
                message=message,
                notification_type=notification_type,
                priority=priority,
                target_users=admin_users,
            )
            return notification
    except Exception as e:
        logger.error(f"Failed to notify admins: {e}")
    return None


# Example usage for system events
def notify_system_error(error_message, user=None):
    """Notify admins about system errors"""
    notify_admins(
        title="System Error Detected",
        message=f"A system error has occurred: {error_message}",
        notification_type="error",
        priority="urgent"
    )


def notify_security_event(event_description, user=None):
    """Notify admins about security events"""
    notify_admins(
        title="Security Event",
        message=f"Security event detected: {event_description}",
        notification_type="warning",
        priority="high"
    )


def notify_maintenance_mode(enabled, user=None):
    """Notify users about maintenance mode"""
    if enabled:
        create_system_notification(
            title="Maintenance Mode Enabled",
            message="The system is currently under maintenance. Some features may be unavailable.",
            notification_type="warning",
            priority="high",
            is_global=True,
            created_by=user,
        )
    else:
        create_system_notification(
            title="Maintenance Mode Disabled",
            message="System maintenance is complete. All features are now available.",
            notification_type="success",
            priority="normal",
            is_global=True,
            created_by=user,
        )
