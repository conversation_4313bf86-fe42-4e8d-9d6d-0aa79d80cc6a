# Backend Requirements for Docker Base Image

# Core Django
Django>=4.2,<5.0
django-environ>=0.10.0

# Django REST Framework
djangorestframework>=3.14.0
djangorestframework-simplejwt>=5.2.0

# API Documentation
drf-spectacular>=0.26.0

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter
mysqlclient>=2.2.0      # MySQL adapter

# Authentication & Authorization
django-guardian>=2.4.0

# CORS handling
django-cors-headers>=4.0.0

# Caching
redis>=4.5.0
django-redis>=5.2.0

# File handling
Pillow>=9.5.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3

# Security
cryptography>=40.0.0

# Development tools
django-debug-toolbar>=4.0.0
django-extensions>=3.2.0

# Testing
pytest>=7.3.0
pytest-django>=4.5.0
pytest-cov>=4.0.0

# Code quality
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0

# Production
gunicorn>=20.1.0
whitenoise>=6.4.0

# Monitoring
sentry-sdk>=1.25.0

# Email
django-anymail>=10.0

# Celery for async tasks
celery>=5.2.0
django-celery-beat>=2.5.0
django-celery-results>=2.5.0

# API rate limiting
django-ratelimit>=4.0.0

# Data validation
marshmallow>=3.19.0

# Timezone handling
django-timezone-field>=5.0

# Model utilities
django-model-utils>=4.3.0

# Admin enhancements
django-admin-interface>=0.19.0

# Logging
structlog>=23.1.0

# Environment management
python-dotenv>=1.0.0

# HTTP client
requests>=2.31.0

# JSON handling
orjson>=3.9.0

# Pagination
django-filter>=23.2

# Permissions (commented out due to version conflicts)
# django-rules>=3.3.0

# Search (commented out to simplify)
# django-haystack>=3.2.1

# Storage (commented out to simplify)
# django-storages>=1.13.0
# boto3>=1.26.0  # For AWS S3

# Backup (commented out to simplify)
# django-dbbackup>=4.0.0

# Health checks (commented out to simplify)
# django-health-check>=3.17.0

# API versioning (commented out to simplify)
# django-rest-framework-api-key>=2.2.0

# Documentation (commented out to simplify)
# sphinx>=7.0.0
# sphinx-rtd-theme>=1.2.0
