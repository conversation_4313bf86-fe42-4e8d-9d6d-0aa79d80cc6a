# 📋 项目模板清单

## 🎯 模板概述

**模板名称**: Django + Vue.js 项目模板  
**版本**: 1.0.0  
**创建日期**: 2025-08-02  
**基于项目**: 密码管理系统  

## 📁 文件结构清单

### 🔧 配置文件模板
- `requirements.txt.template` - Python依赖包模板 (Django 5.0+)
- `project.config.json.template` - 项目配置模板
- `backend/.env.template` - 后端环境变量模板
- `frontend/.env.template` - 前端环境变量模板
- `frontend/package.json.template` - Node.js依赖模板
- `frontend/vite.config.template.ts` - Vite构建配置模板

### 🐍 后端模板文件 (Django)
- `backend/config/settings.template.py` - Django主配置文件
- `backend/config/urls.template.py` - URL路由配置
- `backend/config/wsgi.template.py` - WSGI配置
- `backend/manage.template.py` - Django管理脚本

#### 用户管理应用模板
- `backend/apps/users/models.template.py` - 用户模型
- `backend/apps/users/serializers.template.py` - API序列化器
- `backend/apps/users/views.template.py` - API视图
- `backend/apps/users/urls.template.py` - URL配置
- `backend/apps/users/admin.template.py` - 管理后台配置
- `backend/apps/users/apps.template.py` - 应用配置
- `backend/apps/users/signals.template.py` - 信号处理器
- `backend/apps/users/__init__.template.py` - 应用初始化

### 🎨 前端模板文件 (Vue.js)
- `frontend/src/main.template.ts` - 应用入口文件
- `frontend/src/App.template.vue` - 根组件
- `frontend/src/router/index.template.ts` - 路由配置
- `frontend/src/store/auth.template.ts` - 认证状态管理

### 📚 文档模板
- `README.template.md` - 项目说明文档模板
- `docs/DEVELOPMENT.template.md` - 开发指南模板

### 🚀 脚本工具
- `scripts/init-project.py` - 完整项目初始化器
- `quick-init.py` - 快速项目初始化器

### 📖 使用文档
- `TEMPLATE_USAGE.md` - 模板使用指南
- `TEMPLATE_MANIFEST.md` - 本文件，模板清单

## 🔧 技术栈版本

### 后端依赖 (Python)
- **Django**: >=5.0.0,<5.3.0
- **Django REST Framework**: >=3.16.0,<3.17.0
- **JWT认证**: djangorestframework-simplejwt>=5.5.0
- **API文档**: drf-spectacular>=0.28.0
- **CORS支持**: django-cors-headers>=4.7.0
- **权限管理**: django-guardian>=3.0.0
- **环境配置**: django-environ>=0.12.0

### 前端依赖 (Node.js)
- **Vue.js**: ^3.5.17
- **Vue Router**: ^4.5.1
- **Pinia**: ^3.0.3
- **Ant Design Vue**: ^4.2.6
- **TypeScript**: ^5.8.3
- **Vite**: ^6.3.5
- **Axios**: ^1.10.0

## ✨ 功能特性

### 🔐 认证与授权
- ✅ JWT Token认证
- ✅ 用户注册/登录/登出
- ✅ 密码修改
- ✅ 用户资料管理
- ✅ 基于角色的权限控制

### 🎨 用户界面
- ✅ 响应式设计 (Ant Design Vue)
- ✅ TypeScript类型安全
- ✅ 组件化架构
- ✅ 路由守卫
- ✅ 状态管理 (Pinia)

### 🔧 开发工具
- ✅ API文档自动生成 (Swagger)
- ✅ 开发服务器热重载
- ✅ 代码格式化和检查
- ✅ 环境变量配置
- ✅ 启动脚本

### 📊 可配置功能
- ✅ 数据库选择 (SQLite/MySQL/PostgreSQL)
- ✅ 邮件通知 (可选)
- ✅ 审计日志 (可选)
- ✅ 缓存系统 (可选)
- ✅ 文件上传 (可选)
- ✅ 国际化 (可选)

## 🎯 模板变量

### 项目信息变量
- `快速部署测试` - 项目显示名称
- `快速部署测试` - 项目标题
- `A Django + Vue.js application: 快速部署测试` - 项目描述
- `1.0.0` - 项目版本
- `快速部署测试` - URL友好的项目名称
- `` - 项目主页
- `` - 代码仓库地址
- `MIT` - 许可证类型

### 作者信息变量
- `Developer` - 作者姓名
- `<EMAIL>` - 作者邮箱
- `` - 作者网站

### 技术配置变量
- `5tBDWmsF2Dkr37oNsQMUQ7steMoLzOzJPpYSFiMAG0GX4Z1050vFQKOJr_K_Auyvtuc` - Django密钥 (自动生成)
- `http://localhost:8000` - API基础URL
- `#1890ff` - 主题主色调
- `{{DB_ENGINE}}` - 数据库引擎
- `2025-08-02 13:24:30` - 模板生成日期

### 功能开关变量
- `true` - 启用API文档
- `true` - 启用管理后台
- `true` - 启用跨域支持
- `true` - 启用审计日志
- `false` - 启用邮件通知
- `false` - 启用缓存

## 🚀 使用流程

### 1. 准备阶段
- 确保Python 3.8+和Node.js 18+已安装
- 克隆或下载模板文件

### 2. 初始化项目
```bash
# 快速初始化
python quick-init.py "项目名称"

# 或完整初始化
python scripts/init-project.py "项目名称" --config project.config.json
```

### 3. 环境配置
- 编辑 `backend/.env` 配置后端
- 编辑 `frontend/.env` 配置前端

### 4. 依赖安装
```bash
# 后端依赖
cd backend && pip install -r requirements.txt

# 前端依赖
cd frontend && npm install
```

### 5. 数据库初始化
```bash
cd backend
python manage.py migrate
python manage.py createsuperuser  # 可选
```

### 6. 启动开发服务器
```bash
# 后端 (端口8000)
python manage.py runserver 0.0.0.0:8000

# 前端 (端口5173)
npm run dev
```

## 📈 扩展建议

### 后端扩展
- 添加新的Django应用
- 集成Celery任务队列
- 添加Redis缓存
- 集成第三方认证 (OAuth)
- 添加API版本控制

### 前端扩展
- 添加更多UI组件
- 集成图表库 (ECharts)
- 添加PWA支持
- 集成国际化 (i18n)
- 添加单元测试

### 部署扩展
- Docker容器化
- CI/CD流水线
- 云服务部署
- 监控和日志
- 性能优化

## 🔄 版本历史

### v1.0.0 (2025-08-02)
- ✅ 初始版本发布
- ✅ 基础Django + Vue.js架构
- ✅ 用户认证系统
- ✅ 项目模板化
- ✅ 自动化初始化脚本
- ✅ 完整文档

## 📞 支持与反馈

如果在使用模板过程中遇到问题或有改进建议，请：

1. 检查文档和常见问题
2. 查看生成的项目README
3. 提交Issue或Pull Request
4. 联系模板维护者

---

**模板维护者**: 开发团队  
**最后更新**: 2025-08-02  
**模板状态**: 稳定版本
