"""
Audit URLs for {{PROJECT_NAME}}
Provides comprehensive audit logging and monitoring endpoints
"""

from django.urls import path
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'audit'

# Operation log URLs
operation_log_urlpatterns = [
    path(
        "operation-logs/",
        views.OperationLogListView.as_view(),
        name="operation_log_list",
    ),
]

# Item access log URLs
access_log_urlpatterns = [
    path(
        "access-logs/",
        views.ItemAccessLogListView.as_view(),
        name="item_access_log_list",
    ),
]

# Security event URLs
security_event_urlpatterns = [
    path(
        "security-events/",
        views.SecurityEventListCreateView.as_view(),
        name="security_event_list_create",
    ),
    path(
        "security-events/<uuid:pk>/",
        views.SecurityEventDetailView.as_view(),
        name="security_event_detail",
    ),
    path(
        "security-events/<uuid:event_id>/resolve/",
        views.resolve_security_event,
        name="security_event_resolve",
    ),
]

# Statistics and analytics URLs
stats_urlpatterns = [
    path("stats/", views.audit_stats, name="audit_stats"),
]

# Export URLs
export_urlpatterns = [
    path("export/", views.export_audit_logs, name="audit_export"),
]

# Combine all URL patterns
urlpatterns = (
    operation_log_urlpatterns
    + access_log_urlpatterns
    + security_event_urlpatterns
    + stats_urlpatterns
    + export_urlpatterns
)

# DRF Router (for future ViewSets if needed)
router = DefaultRouter()
# router.register(r'logs', AuditLogViewSet)

urlpatterns += router.urls
