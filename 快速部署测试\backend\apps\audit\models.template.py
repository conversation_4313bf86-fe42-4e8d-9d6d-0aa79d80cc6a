"""
Audit models for 快速部署测试
Provides comprehensive logging and monitoring capabilities
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class OperationLog(models.Model):
    """Operation log model for tracking user actions"""

    ACTION_TYPES = [
        # User operations
        ("user_login", _("User Login")),
        ("user_logout", _("User Logout")),
        ("user_register", _("User Registration")),
        ("user_update", _("User Update")),
        ("user_delete", _("User Deletion")),
        ("password_change", _("Password Change")),
        # Core business operations (customizable)
        ("item_create", _("Create Item")),
        ("item_view", _("View Item")),
        ("item_update", _("Update Item")),
        ("item_delete", _("Delete Item")),
        ("item_copy", _("Copy Item")),
        ("item_export", _("Export Item")),
        ("item_import", _("Import Item")),
        # Sharing operations
        ("item_share", _("Share Item")),
        ("share_revoke", _("Revoke Share")),
        ("onetime_link_create", _("Create One-time Link")),
        ("onetime_link_access", _("Access One-time Link")),
        # Category and tag operations
        ("category_create", _("Create Category")),
        ("category_update", _("Update Category")),
        ("category_delete", _("Delete Category")),
        ("tag_create", _("Create Tag")),
        ("tag_update", _("Update Tag")),
        ("tag_delete", _("Delete Tag")),
        # System operations
        ("system_backup", _("System Backup")),
        ("system_restore", _("System Restore")),
        ("system_setting_update", _("System Settings Update")),
        # Security operations
        ("mfa_enable", _("Enable MFA")),
        ("mfa_disable", _("Disable MFA")),
        ("account_lock", _("Account Lock")),
        ("account_unlock", _("Account Unlock")),
    ]

    RESULT_TYPES = [
        ("success", _("Success")),
        ("failed", _("Failed")),
        ("warning", _("Warning")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("User")
    )
    action_type = models.CharField(
        max_length=30, choices=ACTION_TYPES, verbose_name=_("Action Type")
    )
    result = models.CharField(
        max_length=10, choices=RESULT_TYPES, default="success", verbose_name=_("Result")
    )

    # Operation details
    description = models.TextField(blank=True, verbose_name=_("Description"))
    target_type = models.CharField(
        max_length=50, blank=True, verbose_name=_("Target Type")
    )
    target_id = models.CharField(
        max_length=100, blank=True, verbose_name=_("Target ID")
    )
    target_name = models.CharField(
        max_length=200, blank=True, verbose_name=_("Target Name")
    )

    # Request information
    ip_address = models.GenericIPAddressField(verbose_name=_("IP Address"))
    user_agent = models.TextField(blank=True, verbose_name=_("User Agent"))
    request_method = models.CharField(
        max_length=10, blank=True, verbose_name=_("Request Method")
    )
    request_path = models.CharField(
        max_length=500, blank=True, verbose_name=_("Request Path")
    )

    # Additional data
    extra_data = models.JSONField(
        default=dict, blank=True, verbose_name=_("Extra Data")
    )

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))

    class Meta:
        verbose_name = _("Operation Log")
        verbose_name_plural = _("Operation Logs")
        db_table = "快速部署测试_operation_logs"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["action_type", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        username = self.user.username if self.user else "Anonymous"
        return f"{username} - {self.get_action_type_display()} - {self.created_at}"


class LoginLog(models.Model):
    """Login log model for tracking authentication attempts"""

    LOGIN_TYPES = [
        ("password", _("Password Login")),
        ("mfa", _("Multi-Factor Authentication")),
        ("api_token", _("API Token")),
        ("sso", _("Single Sign-On")),
    ]

    LOGIN_RESULTS = [
        ("success", _("Success")),
        ("failed_password", _("Wrong Password")),
        ("failed_mfa", _("MFA Failed")),
        ("failed_locked", _("Account Locked")),
        ("failed_disabled", _("Account Disabled")),
        ("failed_expired", _("Account Expired")),
        ("failed_other", _("Other Failure")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("User")
    )
    username = models.CharField(
        max_length=150, verbose_name=_("Username")
    )  # Record attempted username
    login_type = models.CharField(
        max_length=20,
        choices=LOGIN_TYPES,
        default="password",
        verbose_name=_("Login Type"),
    )
    result = models.CharField(
        max_length=20, choices=LOGIN_RESULTS, verbose_name=_("Login Result")
    )

    # Login information
    ip_address = models.GenericIPAddressField(verbose_name=_("IP Address"))
    user_agent = models.TextField(blank=True, verbose_name=_("User Agent"))
    location = models.CharField(max_length=200, blank=True, verbose_name=_("Location"))
    device_info = models.JSONField(
        default=dict, blank=True, verbose_name=_("Device Info")
    )

    # Session information
    session_key = models.CharField(
        max_length=40, blank=True, verbose_name=_("Session Key")
    )
    logout_time = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Logout Time")
    )

    # Failure information
    failure_reason = models.TextField(blank=True, verbose_name=_("Failure Reason"))

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Login Time"))

    class Meta:
        verbose_name = _("Login Log")
        verbose_name_plural = _("Login Logs")
        db_table = "快速部署测试_login_logs"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["username", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["result", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        return f"{self.username} - {self.get_result_display()} - {self.created_at}"

    @property
    def session_duration(self):
        """Calculate session duration"""
        if self.logout_time:
            return self.logout_time - self.created_at
        return None


class ItemAccessLog(models.Model):
    """Generic item access log model - can be customized for specific business objects"""

    ACCESS_TYPES = [
        ("view", _("View")),
        ("copy_field", _("Copy Field")),
        ("edit", _("Edit")),
        ("delete", _("Delete")),
        ("share", _("Share")),
        ("export", _("Export")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Generic foreign key fields - customize based on your business objects
    content_type = models.CharField(max_length=50, verbose_name=_("Content Type"))
    object_id = models.CharField(max_length=100, verbose_name=_("Object ID"))
    object_name = models.CharField(max_length=200, verbose_name=_("Object Name"))

    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("User")
    )
    access_type = models.CharField(
        max_length=20, choices=ACCESS_TYPES, verbose_name=_("Access Type")
    )

    # Access information
    ip_address = models.GenericIPAddressField(verbose_name=_("IP Address"))
    user_agent = models.TextField(blank=True, verbose_name=_("User Agent"))

    # Access source
    access_source = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Access Source"),
        help_text=_("direct, share, onetime_link"),
    )
    source_id = models.CharField(
        max_length=100, blank=True, verbose_name=_("Source ID")
    )

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Access Time"))

    class Meta:
        verbose_name = _("Item Access Log")
        verbose_name_plural = _("Item Access Logs")
        db_table = "快速部署测试_item_access_logs"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["content_type", "object_id", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["access_type", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        username = self.user.username if self.user else "Anonymous"
        return f"{self.object_name} - {username} - {self.get_access_type_display()}"


class SecurityEvent(models.Model):
    """Security event model for tracking security incidents"""

    EVENT_TYPES = [
        ("suspicious_login", _("Suspicious Login")),
        ("multiple_failed_logins", _("Multiple Failed Logins")),
        ("account_locked", _("Account Locked")),
        ("data_breach", _("Data Breach")),
        ("unusual_access", _("Unusual Access")),
        ("data_export", _("Data Export")),
        ("bulk_operation", _("Bulk Operation")),
        ("privilege_escalation", _("Privilege Escalation")),
        ("unauthorized_access", _("Unauthorized Access")),
    ]

    SEVERITY_LEVELS = [
        ("low", _("Low")),
        ("medium", _("Medium")),
        ("high", _("High")),
        ("critical", _("Critical")),
    ]

    STATUS_CHOICES = [
        ("open", _("Open")),
        ("investigating", _("Investigating")),
        ("resolved", _("Resolved")),
        ("false_positive", _("False Positive")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event_type = models.CharField(
        max_length=30, choices=EVENT_TYPES, verbose_name=_("Event Type")
    )
    severity = models.CharField(
        max_length=10,
        choices=SEVERITY_LEVELS,
        default="medium",
        verbose_name=_("Severity"),
    )
    status = models.CharField(
        max_length=15, choices=STATUS_CHOICES, default="open", verbose_name=_("Status")
    )

    # Event details
    title = models.CharField(max_length=200, verbose_name=_("Event Title"))
    description = models.TextField(verbose_name=_("Event Description"))

    # Related user and resources
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Related User"),
    )
    affected_resources = models.JSONField(
        default=list, blank=True, verbose_name=_("Affected Resources")
    )

    # Event data
    event_data = models.JSONField(
        default=dict, blank=True, verbose_name=_("Event Data")
    )

    # Processing information
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_security_events",
        verbose_name=_("Assigned To"),
    )
    resolution_notes = models.TextField(blank=True, verbose_name=_("Resolution Notes"))
    resolved_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Resolved At")
    )

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Security Event")
        verbose_name_plural = _("Security Events")
        db_table = "快速部署测试_security_events"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["event_type", "-created_at"]),
            models.Index(fields=["severity", "-created_at"]),
            models.Index(fields=["status", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_severity_display()}"


# Compatibility alias for existing code
AccessLog = ItemAccessLog
