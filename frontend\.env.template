# {{PROJECT_NAME}} Frontend Environment Configuration
# Copy this file to .env.local and update the values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
VITE_APP_NAME={{PROJECT_NAME}}
VITE_APP_TITLE={{PROJECT_TITLE}}
VITE_APP_DESCRIPTION={{PROJECT_DESCRIPTION}}
VITE_APP_VERSION={{PROJECT_VERSION}}
VITE_APP_LOGO={{PROJECT_LOGO}}

# =============================================================================
# API CONFIGURATION
# =============================================================================
VITE_API_BASE_URL={{API_BASE_URL}}
VITE_API_VERSION={{API_VERSION}}
VITE_API_TIMEOUT={{API_TIMEOUT}}

# =============================================================================
# THEME CONFIGURATION
# =============================================================================
VITE_THEME_PRIMARY_COLOR={{THEME_PRIMARY_COLOR}}
VITE_THEME_LAYOUT={{THEME_LAYOUT}}
VITE_THEME_DARK_MODE={{THEME_DARK_MODE}}
VITE_THEME_COMPACT_MODE={{THEME_COMPACT_MODE}}

# =============================================================================
# FEATURE TOGGLES
# =============================================================================
VITE_ENABLE_PWA={{ENABLE_PWA}}
VITE_ENABLE_MOCK={{ENABLE_MOCK}}
VITE_ENABLE_DEVTOOLS={{ENABLE_DEVTOOLS}}
VITE_ENABLE_I18N={{ENABLE_I18N}}
VITE_ENABLE_ANALYTICS={{ENABLE_ANALYTICS}}

# Business Feature Toggles
VITE_ENABLE_USER_MANAGEMENT={{ENABLE_USER_MANAGEMENT}}
VITE_ENABLE_AUDIT_LOG={{ENABLE_AUDIT_LOG}}
VITE_ENABLE_NOTIFICATIONS={{ENABLE_NOTIFICATIONS}}
VITE_ENABLE_FILE_UPLOAD={{ENABLE_FILE_UPLOAD}}
VITE_ENABLE_EXPORT={{ENABLE_EXPORT}}

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
VITE_DEV_SERVER_HOST={{DEV_SERVER_HOST}}
VITE_DEV_SERVER_PORT={{DEV_SERVER_PORT}}
VITE_DEV_SERVER_OPEN={{DEV_SERVER_OPEN}}

# =============================================================================
# BUILD SETTINGS
# =============================================================================
VITE_BUILD_SOURCEMAP={{BUILD_SOURCEMAP}}
VITE_BUILD_ANALYZE={{BUILD_ANALYZE}}
VITE_BUILD_COMPRESS={{BUILD_COMPRESS}}

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
VITE_ENABLE_HTTPS={{ENABLE_HTTPS}}
VITE_CSRF_TOKEN_NAME={{CSRF_TOKEN_NAME}}

# =============================================================================
# THIRD-PARTY SERVICES
# =============================================================================
VITE_GOOGLE_ANALYTICS_ID={{GOOGLE_ANALYTICS_ID}}
VITE_SENTRY_DSN={{SENTRY_DSN}}

# =============================================================================
# UPLOAD CONFIGURATION
# =============================================================================
VITE_MAX_FILE_SIZE={{MAX_FILE_SIZE}}
VITE_ALLOWED_FILE_TYPES={{ALLOWED_FILE_TYPES}}

# =============================================================================
# LOCALIZATION
# =============================================================================
VITE_DEFAULT_LOCALE={{DEFAULT_LOCALE}}
VITE_FALLBACK_LOCALE={{FALLBACK_LOCALE}}
VITE_AVAILABLE_LOCALES={{AVAILABLE_LOCALES}}
