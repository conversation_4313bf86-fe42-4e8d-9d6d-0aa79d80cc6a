# {{PROJECT_NAME}} Development Guide

This guide covers the development workflow, coding standards, and best practices for {{PROJECT_NAME}}.

## 🏗️ Project Structure

```
{{PROJECT_SLUG}}/
├── backend/                 # Django backend
│   ├── config/             # Django settings and configuration
│   ├── apps/               # Django applications
│   │   ├── users/          # User management
│   │   └── core/           # Core business logic
│   ├── core/               # Shared utilities and services
│   ├── static/             # Static files
│   ├── media/              # User uploaded files
│   ├── requirements.txt    # Python dependencies
│   └── manage.py           # Django management script
├── frontend/               # Vue.js frontend
│   ├── src/                # Source code
│   │   ├── components/     # Vue components
│   │   ├── views/          # Page components
│   │   ├── store/          # Pinia stores
│   │   ├── api/            # API client
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript types
│   ├── public/             # Public assets
│   └── package.json        # Node.js dependencies
└── docs/                   # Documentation
```

## 🔧 Development Environment Setup

### Prerequisites

- Python 3.8+
- Node.js 18+
- Git
- Code editor (VS Code recommended)

### Recommended VS Code Extensions

#### Backend Development
- Python
- Django
- Python Docstring Generator
- autoDocstring

#### Frontend Development
- Vetur or Volar (Vue.js)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag

### Environment Variables

#### Backend (.env)
```env
# Development settings
DEBUG=True
SECRET_KEY=your-development-secret-key

# Database
DB_ENGINE=django.db.backends.sqlite3
DB_NAME={{PROJECT_SLUG}}_dev.sqlite3

# API
API_VERSION=v1
ENABLE_API_DOCS=True

# Features
ENABLE_DEBUG_TOOLBAR=True
ENABLE_CORS=True
```

#### Frontend (.env.local)
```env
# Development settings
VITE_API_BASE_URL=http://localhost:8000
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK=false
```

## 🎯 Development Workflow

### Backend Development

1. **Create a new Django app**
   ```bash
   cd backend
   python manage.py startapp app_name
   ```

2. **Add to INSTALLED_APPS**
   ```python
   # config/settings.py
   LOCAL_APPS = [
       'apps.users',
       'apps.app_name',  # Add your new app
   ]
   ```

3. **Create models**
   ```python
   # apps/app_name/models.py
   from django.db import models
   
   class YourModel(models.Model):
       name = models.CharField(max_length=100)
       created_at = models.DateTimeField(auto_now_add=True)
       
       class Meta:
           db_table = '{{PROJECT_SLUG}}_your_model'
   ```

4. **Create and apply migrations**
   ```bash
   python manage.py makemigrations app_name
   python manage.py migrate
   ```

5. **Create serializers**
   ```python
   # apps/app_name/serializers.py
   from rest_framework import serializers
   from .models import YourModel
   
   class YourModelSerializer(serializers.ModelSerializer):
       class Meta:
           model = YourModel
           fields = '__all__'
   ```

6. **Create views**
   ```python
   # apps/app_name/views.py
   from rest_framework import generics
   from .models import YourModel
   from .serializers import YourModelSerializer
   
   class YourModelListView(generics.ListCreateAPIView):
       queryset = YourModel.objects.all()
       serializer_class = YourModelSerializer
   ```

7. **Configure URLs**
   ```python
   # apps/app_name/urls.py
   from django.urls import path
   from . import views
   
   urlpatterns = [
       path('items/', views.YourModelListView.as_view(), name='item-list'),
   ]
   ```

### Frontend Development

1. **Create a new component**
   ```vue
   <!-- src/components/YourComponent.vue -->
   <template>
     <div class="your-component">
       <h1>{{ title }}</h1>
     </div>
   </template>
   
   <script setup lang="ts">
   interface Props {
     title: string
   }
   
   defineProps<Props>()
   </script>
   
   <style scoped>
   .your-component {
     padding: 16px;
   }
   </style>
   ```

2. **Create a new page**
   ```vue
   <!-- src/views/YourPage.vue -->
   <template>
     <div class="page-container">
       <YourComponent :title="pageTitle" />
     </div>
   </template>
   
   <script setup lang="ts">
   import { ref } from 'vue'
   import YourComponent from '@/components/YourComponent.vue'
   
   const pageTitle = ref('Your Page')
   </script>
   ```

3. **Add route**
   ```typescript
   // src/router/index.ts
   const routes = [
     {
       path: '/your-page',
       name: 'YourPage',
       component: () => import('@/views/YourPage.vue'),
       meta: {
         title: 'Your Page - {{PROJECT_NAME}}',
         requiresAuth: true
       }
     }
   ]
   ```

4. **Create API client**
   ```typescript
   // src/api/yourApi.ts
   import { apiClient } from './client'
   
   export interface YourItem {
     id: number
     name: string
     created_at: string
   }
   
   export const yourApi = {
     getItems: () => apiClient.get<YourItem[]>('/api/your-app/items/'),
     createItem: (data: Partial<YourItem>) => apiClient.post<YourItem>('/api/your-app/items/', data),
   }
   ```

5. **Create Pinia store**
   ```typescript
   // src/store/yourStore.ts
   import { defineStore } from 'pinia'
   import { ref } from 'vue'
   import { yourApi, type YourItem } from '@/api/yourApi'
   
   export const useYourStore = defineStore('your-store', () => {
     const items = ref<YourItem[]>([])
     const loading = ref(false)
     
     const fetchItems = async () => {
       loading.value = true
       try {
         const response = await yourApi.getItems()
         items.value = response.data
       } finally {
         loading.value = false
       }
     }
     
     return {
       items,
       loading,
       fetchItems
     }
   })
   ```

## 📏 Coding Standards

### Backend (Python/Django)

- Follow PEP 8 style guide
- Use type hints where possible
- Write docstrings for all functions and classes
- Use meaningful variable and function names
- Keep functions small and focused

```python
from typing import List, Optional
from django.db import models

class User(models.Model):
    """User model with authentication and profile information."""
    
    email: str = models.EmailField(unique=True)
    first_name: str = models.CharField(max_length=50)
    
    def get_full_name(self) -> str:
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
```

### Frontend (TypeScript/Vue)

- Use TypeScript for type safety
- Follow Vue 3 Composition API patterns
- Use meaningful component and variable names
- Keep components small and focused
- Use props and emits with proper typing

```vue
<script setup lang="ts">
interface Props {
  title: string
  items: Item[]
  loading?: boolean
}

interface Emits {
  (e: 'update', item: Item): void
  (e: 'delete', id: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()
</script>
```

## 🧪 Testing

### Backend Testing

```python
# apps/your_app/tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status

User = get_user_model()

class YourModelTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_model_creation(self):
        # Test your model creation
        pass

class YourAPITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_api_endpoint(self):
        response = self.client.get('/api/your-endpoint/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
```

### Frontend Testing

```typescript
// src/components/__tests__/YourComponent.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import YourComponent from '../YourComponent.vue'

describe('YourComponent', () => {
  it('renders properly', () => {
    const wrapper = mount(YourComponent, {
      props: { title: 'Test Title' }
    })
    expect(wrapper.text()).toContain('Test Title')
  })
})
```

## 🔍 Debugging

### Backend Debugging

1. **Django Debug Toolbar** (enabled in development)
   - View SQL queries, cache hits, and performance metrics
   - Access at `/__debug__/` when DEBUG=True

2. **Python Debugger**
   ```python
   import pdb; pdb.set_trace()  # Set breakpoint
   ```

3. **Logging**
   ```python
   import logging
   logger = logging.getLogger(__name__)
   logger.debug('Debug message')
   logger.info('Info message')
   logger.error('Error message')
   ```

### Frontend Debugging

1. **Vue DevTools** (browser extension)
   - Inspect component state and props
   - Monitor Pinia store changes

2. **Browser DevTools**
   - Use console.log() for debugging
   - Set breakpoints in Sources tab

3. **Vite DevTools**
   - Hot module replacement
   - Error overlay in development

## 🚀 Performance Optimization

### Backend

- Use database indexes for frequently queried fields
- Implement caching for expensive operations
- Use select_related() and prefetch_related() for database queries
- Optimize API serializers

### Frontend

- Use lazy loading for routes and components
- Implement virtual scrolling for large lists
- Optimize bundle size with code splitting
- Use proper caching strategies

## 📚 Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Vue.js Documentation](https://vuejs.org/)
- [Ant Design Vue](https://antdv.com/)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

---

Happy coding! 🎉
