#!/bin/bash

# Docker停止脚本 - 用于停止{{PROJECT_NAME}}项目

set -e

echo "🛑 停止{{PROJECT_NAME}}项目..."

# 检查docker-compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose未安装"
    exit 1
fi

# 停止所有服务
echo "⏹️ 停止所有服务..."
docker-compose down

# 可选：清理未使用的镜像和容器
read -p "是否清理未使用的Docker资源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理未使用的Docker资源..."
    docker system prune -f
    echo "✅ 清理完成"
fi

echo "✅ 项目已停止"
