# 🏗️ 项目模板使用指南

这是一个基于当前密码管理系统创建的可复用项目模板，包含完整的Django + Vue.js技术栈。

## 📋 模板特性

### 🔧 技术栈
- **后端**: Django 5.0+ + Django REST Framework
- **前端**: Vue.js 3 + Ant Design Vue + TypeScript + Vite
- **认证**: JWT Token认证
- **数据库**: SQLite (开发) / MySQL/PostgreSQL (生产)
- **API文档**: drf-spectacular (Swagger/OpenAPI)

### ✨ 核心功能
- ✅ 用户认证与授权系统
- ✅ 基于角色的权限控制
- ✅ RESTful API设计
- ✅ 响应式前端界面
- ✅ JWT Token管理
- ✅ API文档自动生成
- ✅ 开发工具集成

### 🎯 模板化特性
- ✅ 配置文件模板化
- ✅ 环境变量标准化
- ✅ 项目信息参数化
- ✅ 功能开关配置
- ✅ 主题配置支持
- ✅ 启动脚本自动生成

## 🚀 快速开始

### 方式1: 使用快速初始化器（推荐）

```bash
# 进入模板目录
cd project-template

# 创建新项目
python quick-init.py "My New Project"

# 进入生成的项目目录
cd my-new-project
```

### 方式2: 使用完整初始化器

```bash
# 使用配置文件初始化
python scripts/init-project.py "My New Project" --config project.config.json

# 或使用交互式配置
python scripts/init-project.py "My New Project"
```

## 📁 生成的项目结构

```
my-new-project/
├── backend/                    # Django后端
│   ├── config/                # Django配置
│   │   ├── settings.py        # 主配置文件
│   │   ├── urls.py            # URL配置
│   │   └── wsgi.py            # WSGI配置
│   ├── apps/                  # Django应用
│   │   └── users/             # 用户管理应用
│   ├── .env                   # 环境变量配置
│   ├── manage.py              # Django管理脚本
│   └── requirements.txt       # Python依赖
├── frontend/                  # Vue.js前端
│   ├── src/                   # 源代码
│   │   ├── components/        # Vue组件
│   │   ├── views/             # 页面组件
│   │   ├── store/             # Pinia状态管理
│   │   ├── api/               # API客户端
│   │   └── router/            # 路由配置
│   ├── .env                   # 前端环境变量
│   ├── package.json           # Node.js依赖
│   └── vite.config.ts         # Vite配置
├── start_backend.bat          # 后端启动脚本 (Windows)
├── start_frontend.bat         # 前端启动脚本 (Windows)
├── start_backend.sh           # 后端启动脚本 (Linux/Mac)
├── start_frontend.sh          # 前端启动脚本 (Linux/Mac)
└── README.md                  # 项目说明文档
```

## ⚙️ 项目配置

### 后端配置 (backend/.env)

```env
# 应用基础信息
APP_NAME=My New Project
APP_DESCRIPTION=项目描述
DEBUG=True
SECRET_KEY=自动生成的密钥

# 数据库配置
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=my-new-project.sqlite3

# 功能开关
ENABLE_API_DOCS=true
ENABLE_ADMIN_INTERFACE=true
ENABLE_CORS=true
ENABLE_AUDIT_LOGGING=true
```

### 前端配置 (frontend/.env)

```env
# 应用信息
VITE_APP_NAME=My New Project
VITE_API_BASE_URL=http://localhost:8000

# 主题配置
VITE_THEME_PRIMARY_COLOR=#1890ff
VITE_THEME_LAYOUT=side

# 功能开关
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_USER_MANAGEMENT=true
```

## 🛠️ 开发环境设置

### 1. 后端设置

```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
.\venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

### 2. 前端设置

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 使用启动脚本

```bash
# Windows
start_backend.bat    # 启动后端
start_frontend.bat   # 启动前端

# Linux/Mac
./start_backend.sh   # 启动后端
./start_frontend.sh  # 启动前端
```

## 🌐 访问地址

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/docs/
- **管理后台**: http://localhost:8000/admin/

## 🔧 自定义配置

### 修改项目信息

编辑生成的配置文件来自定义项目：

1. **后端配置**: `backend/.env`
2. **前端配置**: `frontend/.env`
3. **包信息**: `frontend/package.json`

### 添加新功能

1. **后端**: 在 `backend/apps/` 下创建新的Django应用
2. **前端**: 在 `frontend/src/` 下添加新的组件和页面

### 数据库配置

#### SQLite (默认)
```env
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=project.sqlite3
```

#### MySQL
```env
DB_ENGINE=django.db.backends.mysql
DB_NAME=project_db
DB_HOST=localhost
DB_PORT=3306
DB_USER=username
DB_PASSWORD=password
```

#### PostgreSQL
```env
DB_ENGINE=django.db.backends.postgresql
DB_NAME=project_db
DB_HOST=localhost
DB_PORT=5432
DB_USER=username
DB_PASSWORD=password
```

## 📚 模板文件说明

### 模板变量

模板使用 `{{VARIABLE_NAME}}` 格式的变量，在初始化时会被替换：

- `快速部署测试` - 项目名称
- `快速部署测试` - 项目URL友好名称
- `A Django + Vue.js application: 快速部署测试` - 项目描述
- `5tBDWmsF2Dkr37oNsQMUQ7steMoLzOzJPpYSFiMAG0GX4Z1050vFQKOJr_K_Auyvtuc` - Django密钥（自动生成）
- `Developer` - 作者姓名
- `2025-08-02 13:24:30` - 生成日期

### 配置模板

- `backend/.env.template` - 后端环境变量模板
- `frontend/.env.template` - 前端环境变量模板
- `requirements.txt.template` - Python依赖模板
- `package.json.template` - Node.js依赖模板

## 🎯 最佳实践

### 开发建议

1. **环境隔离**: 使用虚拟环境和环境变量
2. **版本控制**: 及时提交代码变更
3. **代码规范**: 遵循项目代码风格
4. **测试驱动**: 编写单元测试和集成测试
5. **文档更新**: 保持文档与代码同步

### 部署建议

1. **生产配置**: 使用生产环境的配置文件
2. **静态文件**: 配置静态文件服务
3. **数据库**: 使用生产级数据库
4. **安全设置**: 配置HTTPS和安全头
5. **监控日志**: 设置应用监控和日志收集

## 🤝 贡献指南

1. Fork模板仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 等待代码审查

## 📄 许可证

本模板基于MIT许可证发布，可自由使用和修改。

---

**模板版本**: 1.0.0  
**最后更新**: 2025-08-02  
**基于项目**: 密码管理系统
