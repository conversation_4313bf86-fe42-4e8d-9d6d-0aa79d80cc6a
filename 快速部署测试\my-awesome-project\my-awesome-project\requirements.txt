# My Awesome Project - Python Dependencies
# Generated from working environment on 2025-08-02 10:52:43

# Core Django Framework (upgraded to 5.0+)
Django>=5.0.0,<5.3.0
djangorestframework>=3.16.0,<3.17.0

# Database and ORM
mysqlclient>=2.2.0,<2.3.0  # MySQL support (optional)

# Authentication and Security
djangorestframework-simplejwt>=5.5.0,<5.6.0
PyJWT>=2.10.0,<2.11.0
cryptography>=45.0.0,<46.0.0
django-guardian>=3.0.0,<3.1.0  # Object-level permissions

# API Documentation
drf-spectacular>=0.28.0,<0.29.0

# CORS and Security
django-cors-headers>=4.7.0,<4.8.0

# Environment and Configuration
django-environ>=0.12.0,<0.13.0
python-decouple>=3.8,<3.9

# Caching and Performance
django-redis>=6.0.0,<6.1.0
redis>=6.2.0,<6.3.0

# Task Queue (optional)
celery>=5.5.0,<5.6.0
django-celery-beat>=2.8.0,<2.9.0
django-celery-results>=2.6.0,<2.7.0

# File Handling
Pillow>=11.3.0,<11.4.0
openpyxl>=3.1.0,<3.2.0  # Excel support

# Utilities
requests>=2.32.0,<2.33.0
python-dateutil>=2.9.0,<2.10.0
pyotp>=2.9.0,<2.10.0  # Two-factor authentication
qrcode>=8.2,<8.3  # QR code generation

# Development and Testing
django-debug-toolbar>=6.0.0,<6.1.0
django-extensions>=4.1,<4.2
factory-boy>=3.3.0,<3.4.0
Faker>=37.4.0,<37.5.0
pytest>=8.4.0,<8.5.0
pytest-django>=4.11.0,<4.12.0
coverage>=7.10.0,<7.11.0

# Code Quality
black>=25.1.0,<25.2.0
isort>=6.0.0,<6.1.0
flake8>=7.3.0,<7.4.0
pylint>=3.3.0,<3.4.0

# Production Server
gunicorn>=23.0.0,<23.1.0

# Additional utilities from current environment
django-filter>=25.1,<25.2
django-timezone-field>=7.1,<7.2
marshmallow>=4.0.0,<4.1.0  # Serialization
psutil>=7.0.0,<7.1.0  # System monitoring
PyYAML>=6.0.0,<6.1.0  # YAML support
