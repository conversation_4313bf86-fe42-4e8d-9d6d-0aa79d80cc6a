@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 {{PROJECT_NAME}} 快速部署脚本
echo 使用预构建基础镜像，适合内网环境
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 检查基础镜像是否存在
set BACKEND_IMAGE=locker-template-backend-base:latest
set FRONTEND_IMAGE=locker-template-frontend-base:latest

echo 🔍 检查基础镜像...
docker image inspect %BACKEND_IMAGE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 后端基础镜像不存在: %BACKEND_IMAGE%
    echo 请先运行构建脚本: scripts\build-images.bat
    echo 或导入预构建的镜像文件
    pause
    exit /b 1
)

docker image inspect %FRONTEND_IMAGE% >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端基础镜像不存在: %FRONTEND_IMAGE%
    echo 请先运行构建脚本: scripts\build-images.bat
    echo 或导入预构建的镜像文件
    pause
    exit /b 1
)

echo ✅ 基础镜像检查通过

REM 创建环境变量文件（如果不存在）
if not exist .env (
    echo 📝 创建环境变量文件...
    copy .env.docker.template .env >nul
    echo ✅ 已创建 .env 文件，请根据需要修改配置
)

REM 使用基础镜像启动服务
echo 🔧 启动服务（使用基础镜像）...
docker-compose -f docker-compose.base.yml up -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 15 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose -f docker-compose.base.yml ps | findstr "Up" >nul
if errorlevel 1 (
    echo ❌ 服务启动失败，查看日志：
    docker-compose -f docker-compose.base.yml logs
    pause
    exit /b 1
) else (
    echo ✅ 服务启动成功
)

REM 等待数据库就绪
echo ⏳ 等待数据库就绪...
:wait_db
docker-compose -f docker-compose.base.yml exec -T db pg_isready -U {{PROJECT_SLUG}}_user >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_db
)

REM 运行数据库迁移
echo 🗄️ 运行数据库迁移...
docker-compose -f docker-compose.base.yml exec -T backend python manage.py migrate

REM 初始化系统设置
echo ⚙️ 初始化系统设置...
docker-compose -f docker-compose.base.yml exec -T backend python manage.py init_system_settings

REM 创建超级用户
echo 👤 检查超级用户...
docker-compose -f docker-compose.base.yml exec -T backend python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else print('超级用户已存在')"

REM 收集静态文件
echo 📁 收集静态文件...
docker-compose -f docker-compose.base.yml exec -T backend python manage.py collectstatic --noinput

echo.
echo 🎉 部署完成！
echo.
echo 🌐 访问地址：
echo   前端应用: http://localhost:5173
echo   后端API: http://localhost:8001
echo   API文档: http://localhost:8001/api/docs/
echo   Django Admin: http://localhost:8001/admin/
echo.
echo 🔑 默认管理员账户：
echo   用户名: admin
echo   密码: admin123
echo.
echo 📋 管理命令：
echo   查看状态: docker-compose -f docker-compose.base.yml ps
echo   查看日志: docker-compose -f docker-compose.base.yml logs -f
echo   停止服务: docker-compose -f docker-compose.base.yml down
echo   重启服务: docker-compose -f docker-compose.base.yml restart
echo.
echo 💡 提示：
echo   - 首次启动可能需要几分钟时间
echo   - 如遇问题，请查看日志文件
echo   - 生产环境请修改默认密码
echo.
pause
