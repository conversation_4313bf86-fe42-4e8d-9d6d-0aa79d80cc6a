"""
Management command to initialize system settings for {{PROJECT_NAME}}
"""

from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from apps.system.models import SystemSetting, EmailTemplate


class Command(BaseCommand):
    help = 'Initialize default system settings and email templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing settings',
        )

    def handle(self, *args, **options):
        force_update = options['force']
        
        self.stdout.write(self.style.SUCCESS('Initializing system settings...'))
        
        # Define default system settings
        default_settings = [
            # Security Settings
            {
                'key': 'session_timeout_minutes',
                'name': 'Session Timeout (Minutes)',
                'description': 'User session timeout in minutes',
                'category': 'security',
                'value_type': 'integer',
                'value': '30',
                'default_value': '30',
                'validation_rules': {'min_value': 5, 'max_value': 1440},
                'is_public': False,
                'requires_restart': False,
            },
            {
                'key': 'max_login_attempts',
                'name': 'Maximum Login Attempts',
                'description': 'Maximum failed login attempts before account lockout',
                'category': 'security',
                'value_type': 'integer',
                'value': '5',
                'default_value': '5',
                'validation_rules': {'min_value': 3, 'max_value': 10},
                'is_public': False,
                'requires_restart': False,
            },
            {
                'key': 'account_lockout_minutes',
                'name': 'Account Lockout Duration (Minutes)',
                'description': 'Duration to lock account after max failed attempts',
                'category': 'security',
                'value_type': 'integer',
                'value': '30',
                'default_value': '30',
                'validation_rules': {'min_value': 5, 'max_value': 1440},
                'is_public': False,
                'requires_restart': False,
            },
            
            # Email Settings
            {
                'key': 'smtp_host',
                'name': 'SMTP Host',
                'description': 'SMTP server hostname',
                'category': 'email',
                'value_type': 'string',
                'value': 'smtp.gmail.com',
                'default_value': 'smtp.gmail.com',
                'is_public': False,
                'requires_restart': True,
            },
            {
                'key': 'smtp_port',
                'name': 'SMTP Port',
                'description': 'SMTP server port',
                'category': 'email',
                'value_type': 'integer',
                'value': '587',
                'default_value': '587',
                'validation_rules': {'min_value': 1, 'max_value': 65535},
                'is_public': False,
                'requires_restart': True,
            },
            {
                'key': 'smtp_use_tls',
                'name': 'SMTP Use TLS',
                'description': 'Enable TLS for SMTP connection',
                'category': 'email',
                'value_type': 'boolean',
                'value': 'true',
                'default_value': 'true',
                'is_public': False,
                'requires_restart': True,
            },
            {
                'key': 'smtp_from_email',
                'name': 'From Email Address',
                'description': 'Default from email address',
                'category': 'email',
                'value_type': 'string',
                'value': 'noreply@{{DOMAIN}}',
                'default_value': 'noreply@{{DOMAIN}}',
                'is_public': False,
                'requires_restart': False,
            },
            
            # UI Settings
            {
                'key': 'site_title',
                'name': 'Site Title',
                'description': 'Website title displayed in browser',
                'category': 'ui',
                'value_type': 'string',
                'value': '{{PROJECT_NAME}}',
                'default_value': '{{PROJECT_NAME}}',
                'is_public': True,
                'requires_restart': False,
            },
            {
                'key': 'site_description',
                'name': 'Site Description',
                'description': 'Website description for SEO',
                'category': 'ui',
                'value_type': 'text',
                'value': '{{PROJECT_DESCRIPTION}}',
                'default_value': '{{PROJECT_DESCRIPTION}}',
                'is_public': True,
                'requires_restart': False,
            },
            {
                'key': 'items_per_page',
                'name': 'Items Per Page',
                'description': 'Default number of items per page in lists',
                'category': 'ui',
                'value_type': 'integer',
                'value': '20',
                'default_value': '20',
                'validation_rules': {'min_value': 5, 'max_value': 100},
                'is_public': True,
                'requires_restart': False,
            },
            
            # System Settings
            {
                'key': 'maintenance_mode',
                'name': 'Maintenance Mode',
                'description': 'Enable maintenance mode',
                'category': 'system',
                'value_type': 'boolean',
                'value': 'false',
                'default_value': 'false',
                'is_public': True,
                'requires_restart': False,
            },
            {
                'key': 'maintenance_message',
                'name': 'Maintenance Message',
                'description': 'Message displayed during maintenance',
                'category': 'system',
                'value_type': 'text',
                'value': 'System is under maintenance. Please try again later.',
                'default_value': 'System is under maintenance. Please try again later.',
                'is_public': True,
                'requires_restart': False,
            },
            
            # Audit Settings
            {
                'key': 'audit_log_retention_days',
                'name': 'Audit Log Retention (Days)',
                'description': 'Number of days to retain audit logs',
                'category': 'audit',
                'value_type': 'integer',
                'value': '90',
                'default_value': '90',
                'validation_rules': {'min_value': 30, 'max_value': 365},
                'is_public': False,
                'requires_restart': False,
            },
            {
                'key': 'enable_detailed_logging',
                'name': 'Enable Detailed Logging',
                'description': 'Enable detailed audit logging',
                'category': 'audit',
                'value_type': 'boolean',
                'value': 'true',
                'default_value': 'true',
                'is_public': False,
                'requires_restart': False,
            },
        ]
        
        # Create or update settings
        created_count = 0
        updated_count = 0
        
        for setting_data in default_settings:
            setting, created = SystemSetting.objects.get_or_create(
                key=setting_data['key'],
                defaults=setting_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(f"Created setting: {setting_data['key']}")
            elif force_update:
                for field, value in setting_data.items():
                    if field != 'key':
                        setattr(setting, field, value)
                setting.save()
                updated_count += 1
                self.stdout.write(f"Updated setting: {setting_data['key']}")
        
        # Initialize default email templates
        self.stdout.write(self.style.SUCCESS('Initializing email templates...'))
        
        default_templates = [
            {
                'name': 'Welcome Email',
                'template_type': 'welcome',
                'subject': 'Welcome to {{PROJECT_NAME}}',
                'content': '''
                <h1>Welcome to {{PROJECT_NAME}}!</h1>
                <p>Hello {{user_name}},</p>
                <p>Welcome to {{PROJECT_NAME}}. Your account has been successfully created.</p>
                <p>You can now log in and start using the system.</p>
                <p>Best regards,<br>{{PROJECT_NAME}} Team</p>
                ''',
                'is_active': True,
                'is_default': True,
            },
            {
                'name': 'Password Reset',
                'template_type': 'password_reset',
                'subject': 'Password Reset Request',
                'content': '''
                <h1>Password Reset Request</h1>
                <p>Hello {{user_name}},</p>
                <p>You have requested to reset your password for {{PROJECT_NAME}}.</p>
                <p>Click the link below to reset your password:</p>
                <p><a href="{{reset_link}}">Reset Password</a></p>
                <p>If you did not request this, please ignore this email.</p>
                <p>Best regards,<br>{{PROJECT_NAME}} Team</p>
                ''',
                'is_active': True,
                'is_default': True,
            },
            {
                'name': 'Account Locked',
                'template_type': 'account_locked',
                'subject': 'Account Locked - Security Alert',
                'content': '''
                <h1>Account Locked - Security Alert</h1>
                <p>Hello {{user_name}},</p>
                <p>Your account has been locked due to multiple failed login attempts.</p>
                <p>Your account will be automatically unlocked after {{lockout_duration}} minutes.</p>
                <p>If this was not you, please contact support immediately.</p>
                <p>Best regards,<br>{{PROJECT_NAME}} Team</p>
                ''',
                'is_active': True,
                'is_default': True,
            },
        ]
        
        template_created_count = 0
        template_updated_count = 0
        
        for template_data in default_templates:
            template, created = EmailTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                is_default=True,
                defaults=template_data
            )
            
            if created:
                template_created_count += 1
                self.stdout.write(f"Created email template: {template_data['template_type']}")
            elif force_update:
                for field, value in template_data.items():
                    if field not in ['template_type', 'is_default']:
                        setattr(template, field, value)
                template.save()
                template_updated_count += 1
                self.stdout.write(f"Updated email template: {template_data['template_type']}")
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nInitialization complete!\n'
                f'Settings: {created_count} created, {updated_count} updated\n'
                f'Email Templates: {template_created_count} created, {template_updated_count} updated'
            )
        )
