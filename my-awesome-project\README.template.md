# My Awesome Project

A Django + Vue.js application: My Awesome Project

## 🚀 Quick Start

### Prerequisites

- **Python 3.8+** - Backend development
- **Node.js 18+** - Frontend development
- **npm or yarn** - Package management
- **Git** - Version control

### Installation

1. **Clone the repository**
   ```bash
   git clone 
   cd my-awesome-project
   ```

2. **Backend Setup**
   ```bash
   cd backend
   
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # Windows
   .\venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Setup environment variables
   cp .env.template .env
   # Edit .env file with your configuration
   
   # Run database migrations
   python manage.py migrate
   
   # Create superuser (optional)
   python manage.py createsuperuser
   
   # Start development server
   python manage.py runserver 0.0.0.0:8000
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   
   # Install dependencies
   npm install
   
   # Setup environment variables
   cp .env.template .env.local
   # Edit .env.local file with your configuration
   
   # Start development server
   npm run dev
   ```

### Quick Start Scripts

For convenience, you can use the provided startup scripts:

- **Windows**: 
  - `start_backend.bat` - Start backend server
  - `start_frontend.bat` - Start frontend server
  
- **Linux/Mac**: 
  - `./start_backend.sh` - Start backend server
  - `./start_frontend.sh` - Start frontend server

## 📚 Documentation

- **API Documentation**: http://localhost:8000/api/docs/
- **Admin Interface**: http://localhost:8000/admin/
- **Frontend Application**: http://localhost:5173

## 🏗️ Architecture

### Backend (Django + DRF)

- **Framework**: Django 5.0+ with Django REST Framework
- **Authentication**: JWT-based authentication
- **Database**: SQLite (development) / PostgreSQL/MySQL (production)
- **API Documentation**: drf-spectacular (Swagger/OpenAPI)

### Frontend (Vue.js + Ant Design)

- **Framework**: Vue.js 3 with Composition API
- **UI Library**: Ant Design Vue
- **State Management**: Pinia
- **Build Tool**: Vite
- **Language**: TypeScript

### Key Features

- ✅ **User Authentication & Authorization**
- ✅ **RESTful API Design**
- ✅ **Responsive UI with Ant Design**
- ✅ **JWT Token Management**
- ✅ **Role-based Access Control**
- ✅ **API Documentation**
- ✅ **Development Tools Integration**

## 🔧 Configuration

### Backend Configuration

Copy `backend/.env.template` to `backend/.env` and configure:

```env
# Application
APP_NAME=My Awesome Project
DEBUG=True
SECRET_KEY=your-secret-key

# Database
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=my-awesome-project.sqlite3

# Security
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:5173
```

### Frontend Configuration

Copy `frontend/.env.template` to `frontend/.env.local` and configure:

```env
# Application
VITE_APP_NAME=My Awesome Project
VITE_API_BASE_URL=http://localhost:8000

# Theme
VITE_THEME_PRIMARY_COLOR=#1890ff
VITE_THEME_LAYOUT=side
```

## 🛠️ Development

### Backend Development

```bash
cd backend

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
.\venv\Scripts\activate   # Windows

# Install new dependencies
pip install package-name
pip freeze > requirements.txt

# Create new Django app
python manage.py startapp app_name

# Make migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Run tests
python manage.py test

# Collect static files
python manage.py collectstatic
```

### Frontend Development

```bash
cd frontend

# Install new dependencies
npm install package-name

# Run development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run tests
npm run test

# Lint code
npm run lint

# Format code
npm run format
```

## 🚀 Deployment

### Backend Deployment

1. **Environment Setup**
   ```bash
   # Production environment variables
   DEBUG=False
   ALLOWED_HOSTS=your-domain.com
   SECRET_KEY=your-production-secret-key
   
   # Database (PostgreSQL example)
   DB_ENGINE=django.db.backends.postgresql
   DB_NAME=your_db_name
   DB_HOST=your_db_host
   DB_PORT=5432
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   ```

2. **Static Files**
   ```bash
   python manage.py collectstatic --noinput
   ```

3. **Database Migration**
   ```bash
   python manage.py migrate
   ```

4. **WSGI Server** (using Gunicorn)
   ```bash
   gunicorn config.wsgi:application --bind 0.0.0.0:8000
   ```

### Frontend Deployment

1. **Build for Production**
   ```bash
   npm run build
   ```

2. **Deploy** the `dist/` folder to your web server

## 🧪 Testing

### Backend Tests

```bash
cd backend
python manage.py test
```

### Frontend Tests

```bash
cd frontend
npm run test
```

## 📝 API Documentation

The API documentation is automatically generated and available at:
- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

- **Developer** - *Initial work* - [<EMAIL>](mailto:<EMAIL>)

## 🙏 Acknowledgments

- Django and Django REST Framework teams
- Vue.js and Ant Design Vue teams
- All contributors and open source projects that made this possible

---

**Generated from template on 2025-08-02 10:52:43**
