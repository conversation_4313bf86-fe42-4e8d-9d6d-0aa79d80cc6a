"""
System models for {{PROJECT_NAME}}
Based on the existing system management functionality
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class SystemSetting(models.Model):
    """System settings model for configuration management"""

    SETTING_TYPES = [
        ("string", _("String")),
        ("integer", _("Integer")),
        ("float", _("Float")),
        ("boolean", _("Boolean")),
        ("json", _("JSON")),
        ("text", _("Text")),
    ]

    CATEGORIES = [
        ("security", _("Security Settings")),
        ("notification", _("Notification Settings")),
        ("ui", _("UI Settings")),
        ("integration", _("Integration Settings")),
        ("audit", _("Audit Settings")),
        ("system", _("System Settings")),
        ("backup", _("Backup Settings")),
        ("email", _("Email Settings")),
    ]

    key = models.CharField(max_length=100, unique=True, verbose_name=_("Setting Key"))
    name = models.CharField(max_length=200, verbose_name=_("Setting Name"))
    description = models.TextField(blank=True, verbose_name=_("Setting Description"))

    category = models.CharField(
        max_length=20, choices=CATEGORIES, default="system", verbose_name=_("Category")
    )

    value_type = models.CharField(
        max_length=10, choices=SETTING_TYPES, default="string", verbose_name=_("Value Type")
    )

    value = models.TextField(verbose_name=_("Setting Value"))
    default_value = models.TextField(verbose_name=_("Default Value"))

    # Validation rules
    validation_rules = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_("Validation Rules"),
        help_text=_("JSON format validation rules"),
    )

    # Setting properties
    is_public = models.BooleanField(default=False, verbose_name=_("Public Setting"))
    is_readonly = models.BooleanField(default=False, verbose_name=_("Read-only Setting"))
    requires_restart = models.BooleanField(default=False, verbose_name=_("Requires Restart"))

    # Ordering and grouping
    order = models.IntegerField(default=0, verbose_name=_("Order"))
    group = models.CharField(max_length=50, blank=True, verbose_name=_("Group"))

    # Modification tracking
    modified_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("Modified By")
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("System Setting")
        verbose_name_plural = _("System Settings")
        db_table = "{{PROJECT_SLUG}}_system_settings"
        ordering = ["category", "order", "name"]

    def __str__(self):
        return f"{self.name} ({self.key})"

    def get_typed_value(self):
        """Get typed value based on value_type"""
        if self.value_type == "integer":
            return int(self.value)
        elif self.value_type == "float":
            return float(self.value)
        elif self.value_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.value_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value

    def set_typed_value(self, value):
        """Set typed value based on value_type"""
        if self.value_type == "json":
            import json
            self.value = json.dumps(value)
        else:
            self.value = str(value)

    def validate_value(self, value):
        """Validate setting value against validation rules"""
        if not self.validation_rules:
            return True, None

        try:
            rules = self.validation_rules

            # Check required
            if rules.get("required", False) and not value:
                return False, _("This setting is required")

            # Check min/max values for numeric types
            if self.value_type in ["integer", "float"]:
                num_value = float(value) if self.value_type == "float" else int(value)

                if "min_value" in rules and num_value < rules["min_value"]:
                    return False, _(f"Value cannot be less than {rules['min_value']}")

                if "max_value" in rules and num_value > rules["max_value"]:
                    return False, _(f"Value cannot be greater than {rules['max_value']}")

            # Check string length
            if self.value_type in ["string", "text"]:
                if "min_length" in rules and len(value) < rules["min_length"]:
                    return False, _(f"Length cannot be less than {rules['min_length']} characters")

                if "max_length" in rules and len(value) > rules["max_length"]:
                    return False, _(f"Length cannot exceed {rules['max_length']} characters")

            # Check regex pattern
            if "pattern" in rules:
                import re
                if not re.match(rules["pattern"], str(value)):
                    return False, _("Value format is incorrect")

            # Check choices
            if "choices" in rules:
                if value not in rules["choices"]:
                    return False, _(f"Value must be one of: {', '.join(rules['choices'])}")

            return True, None

        except Exception as e:
            return False, str(e)


class SystemNotification(models.Model):
    """System notification model"""

    NOTIFICATION_TYPES = [
        ("info", _("Information")),
        ("warning", _("Warning")),
        ("error", _("Error")),
        ("success", _("Success")),
    ]

    PRIORITY_LEVELS = [
        ("low", _("Low")),
        ("normal", _("Normal")),
        ("high", _("High")),
        ("urgent", _("Urgent")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200, verbose_name=_("Notification Title"))
    message = models.TextField(verbose_name=_("Notification Content"))

    notification_type = models.CharField(
        max_length=10,
        choices=NOTIFICATION_TYPES,
        default="info",
        verbose_name=_("Notification Type"),
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default="normal",
        verbose_name=_("Priority"),
    )

    # Target users
    target_users = models.ManyToManyField(
        User,
        through="NotificationRecipient",
        related_name="notifications",
        verbose_name=_("Target Users"),
    )

    # Notification settings
    is_global = models.BooleanField(default=False, verbose_name=_("Global Notification"))
    is_persistent = models.BooleanField(default=False, verbose_name=_("Persistent Notification"))
    auto_dismiss = models.BooleanField(default=True, verbose_name=_("Auto Dismiss"))
    dismiss_after = models.IntegerField(
        null=True, blank=True, verbose_name=_("Dismiss After (seconds)")
    )

    # Action button
    action_url = models.URLField(blank=True, verbose_name=_("Action URL"))
    action_text = models.CharField(
        max_length=50, blank=True, verbose_name=_("Action Text")
    )

    # Sender
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_notifications",
        verbose_name=_("Created By"),
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name=_("Expires At"))

    class Meta:
        verbose_name = _("System Notification")
        verbose_name_plural = _("System Notifications")
        db_table = "{{PROJECT_SLUG}}_system_notifications"
        ordering = ["-created_at"]

    def __str__(self):
        return self.title

    @property
    def is_expired(self):
        """Check if notification is expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False


class NotificationRecipient(models.Model):
    """Notification recipient model"""

    notification = models.ForeignKey(
        SystemNotification, on_delete=models.CASCADE, verbose_name=_("Notification")
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("User"))

    # Status
    is_read = models.BooleanField(default=False, verbose_name=_("Is Read"))
    is_dismissed = models.BooleanField(default=False, verbose_name=_("Is Dismissed"))

    # Timestamps
    read_at = models.DateTimeField(null=True, blank=True, verbose_name=_("Read At"))
    dismissed_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Dismissed At")
    )

    class Meta:
        verbose_name = _("Notification Recipient")
        verbose_name_plural = _("Notification Recipients")
        db_table = "{{PROJECT_SLUG}}_notification_recipients"
        unique_together = ["notification", "user"]

    def __str__(self):
        return f"{self.notification.title} - {self.user.username}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            from django.utils import timezone
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=["is_read", "read_at"])

    def dismiss(self):
        """Dismiss notification"""
        if not self.is_dismissed:
            from django.utils import timezone
            self.is_dismissed = True
            self.dismissed_at = timezone.now()
            self.save(update_fields=["is_dismissed", "dismissed_at"])


class EmailTemplate(models.Model):
    """Email template model"""

    TEMPLATE_TYPES = [
        ("welcome", _("Welcome Email")),
        ("password_reset", _("Password Reset")),
        ("account_locked", _("Account Locked")),
        ("security_alert", _("Security Alert")),
        ("system_maintenance", _("System Maintenance")),
        ("custom", _("Custom")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name=_("Template Name"))
    template_type = models.CharField(
        max_length=20, choices=TEMPLATE_TYPES, verbose_name=_("Template Type")
    )
    subject = models.CharField(max_length=200, verbose_name=_("Email Subject"))
    content = models.TextField(verbose_name=_("Email Content"))

    # Template settings
    is_active = models.BooleanField(default=True, verbose_name=_("Is Active"))
    is_default = models.BooleanField(default=False, verbose_name=_("Default Template"))

    # Creation info
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("Created By")
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Email Template")
        verbose_name_plural = _("Email Templates")
        db_table = "{{PROJECT_SLUG}}_email_templates"
        ordering = ["template_type", "name"]
        unique_together = ["template_type", "is_default"]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"
