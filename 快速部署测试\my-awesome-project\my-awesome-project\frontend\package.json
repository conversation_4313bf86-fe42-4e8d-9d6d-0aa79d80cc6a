{"name": "my-awesome-project-frontend", "version": "1.0.0", "description": "A Django + Vue.js application: My Awesome Project - Frontend Application", "private": true, "type": "module", "keywords": ["vue", "vue3", "typescript", "vite", "ant-design-vue", "my-awesome-project"], "homepage": "", "repository": {"type": "git", "url": ""}, "license": "MIT", "author": {"name": "Developer", "email": "<EMAIL>", "url": ""}, "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit && vite build --mode production", "build:dev": "vite build --mode development", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:style": "stylelint **/*.{vue,css,scss,postcss,less} --fix", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.5.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "ant-design-vue": "^4.2.6", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.10.0", "@vueuse/core": "^13.4.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "qrcode": "^1.5.4", "vue-i18n": "^11.1.7"}, "devDependencies": {"@types/node": "^22.16.0", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/test-utils": "^2.4.6", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "2.2.10", "vitest": "^3.2.4", "happy-dom": "^17.6.3", "@vitest/ui": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.30.1", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint-plugin-vue": "^10.2.0", "prettier": "^3.6.2", "stylelint": "^16.21.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-recommended-vue": "^1.6.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite-plugin-pwa": "^1.0.1", "vite-plugin-vue-devtools": "^7.7.7"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}