# {{PROJECT_NAME}} Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME={{PROJECT_NAME}}
APP_DESCRIPTION={{PROJECT_DESCRIPTION}}
APP_VERSION=1.0.0
DEBUG={{DEBUG}}
SECRET_KEY={{SECRET_KEY}}

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQLite (default for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME={{PROJECT_SLUG}}.sqlite3

# MySQL (uncomment and configure for production)
# DB_ENGINE=django.db.backends.mysql
# DB_NAME={{PROJECT_SLUG}}_db
# DB_HOST=localhost
# DB_PORT=3306
# DB_USER={{DB_USER}}
# DB_PASSWORD={{DB_PASSWORD}}

# PostgreSQL (alternative option)
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME={{PROJECT_SLUG}}_db
# DB_HOST=localhost
# DB_PORT=5432
# DB_USER={{DB_USER}}
# DB_PASSWORD={{DB_PASSWORD}}

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
ALLOWED_HOSTS={{ALLOWED_HOSTS}}
CORS_ALLOWED_ORIGINS={{CORS_ALLOWED_ORIGINS}}
CORS_ALLOW_CREDENTIALS=True

# JWT Configuration
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=7  # days
JWT_ALGORITHM=HS256

# =============================================================================
# FEATURE TOGGLES
# =============================================================================
ENABLE_API_DOCS={{ENABLE_API_DOCS}}
ENABLE_ADMIN_INTERFACE={{ENABLE_ADMIN_INTERFACE}}
ENABLE_DEBUG_TOOLBAR={{ENABLE_DEBUG_TOOLBAR}}
ENABLE_CORS={{ENABLE_CORS}}

# Optional Features
ENABLE_EMAIL_NOTIFICATIONS={{ENABLE_EMAIL_NOTIFICATIONS}}
ENABLE_SMS_NOTIFICATIONS={{ENABLE_SMS_NOTIFICATIONS}}
ENABLE_AUDIT_LOGGING={{ENABLE_AUDIT_LOGGING}}
ENABLE_RATE_LIMITING={{ENABLE_RATE_LIMITING}}
ENABLE_CACHING={{ENABLE_CACHING}}

# =============================================================================
# EMAIL CONFIGURATION (if enabled)
# =============================================================================
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST={{EMAIL_HOST}}
EMAIL_PORT={{EMAIL_PORT}}
EMAIL_USE_TLS={{EMAIL_USE_TLS}}
EMAIL_HOST_USER={{EMAIL_HOST_USER}}
EMAIL_HOST_PASSWORD={{EMAIL_HOST_PASSWORD}}
DEFAULT_FROM_EMAIL={{DEFAULT_FROM_EMAIL}}

# =============================================================================
# REDIS CONFIGURATION (if caching enabled)
# =============================================================================
REDIS_URL={{REDIS_URL}}
CACHE_TTL={{CACHE_TTL}}

# =============================================================================
# CELERY CONFIGURATION (if background tasks enabled)
# =============================================================================
CELERY_BROKER_URL={{CELERY_BROKER_URL}}
CELERY_RESULT_BACKEND={{CELERY_RESULT_BACKEND}}

# =============================================================================
# FILE STORAGE
# =============================================================================
MEDIA_ROOT={{MEDIA_ROOT}}
STATIC_ROOT={{STATIC_ROOT}}
MAX_UPLOAD_SIZE={{MAX_UPLOAD_SIZE}}

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL={{LOG_LEVEL}}
LOG_FILE={{LOG_FILE}}

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_VERSION={{API_VERSION}}
API_TITLE={{API_TITLE}}
API_DESCRIPTION={{API_DESCRIPTION}}

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
DEVELOPMENT_SERVER_HOST={{DEVELOPMENT_SERVER_HOST}}
DEVELOPMENT_SERVER_PORT={{DEVELOPMENT_SERVER_PORT}}
