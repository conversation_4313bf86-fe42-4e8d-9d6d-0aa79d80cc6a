@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🛑 停止{{PROJECT_NAME}}项目...

REM 检查docker-compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose未安装
    pause
    exit /b 1
)

REM 停止所有服务
echo ⏹️ 停止所有服务...
docker-compose down

REM 询问是否清理资源
set /p cleanup="是否清理未使用的Docker资源？(y/N): "
if /i "!cleanup!"=="y" (
    echo 🧹 清理未使用的Docker资源...
    docker system prune -f
    echo ✅ 清理完成
)

echo ✅ 项目已停止
pause
