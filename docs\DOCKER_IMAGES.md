# Docker基础镜像构建和使用指南

本文档介绍如何构建和使用{{PROJECT_NAME}}项目的基础镜像，以实现快速部署和内网环境友好的容器化方案。

## 🎯 基础镜像优势

### 传统方式的问题
- 每次部署都需要下载依赖包
- 网络环境要求高，内网部署困难
- 构建时间长，影响开发效率
- 依赖版本不一致可能导致问题

### 基础镜像的优势
- ✅ **预安装依赖**: 所有Python和Node.js依赖都已预装
- ✅ **内网友好**: 无需联网下载依赖，支持离线部署
- ✅ **快速启动**: 跳过依赖安装步骤，启动速度快
- ✅ **版本一致**: 确保所有环境使用相同的依赖版本
- ✅ **体积优化**: 通过多阶段构建和缓存优化镜像大小

## 🏗️ 构建基础镜像

### 前置要求
- Docker Desktop 已安装并运行
- 至少 4GB 可用磁盘空间
- 稳定的网络连接（仅构建时需要）

### 自动构建

**Windows:**
```bash
scripts\build-images.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/build-images.sh
./scripts/build-images.sh
```

### 手动构建

#### 构建后端基础镜像
```bash
cd backend
docker build -t locker-template-backend-base:latest .
```

#### 构建前端基础镜像
```bash
cd frontend
docker build -t locker-template-frontend-base:latest .
```

### 构建过程说明

#### 后端镜像构建过程
1. **基础镜像**: Python 3.11-slim
2. **系统依赖**: 安装编译工具和数据库驱动
3. **Python依赖**: 安装requirements.txt中的所有包
4. **用户配置**: 创建非root用户
5. **启动脚本**: 配置自动化启动流程

#### 前端镜像构建过程
1. **基础镜像**: Node.js 18-alpine
2. **系统依赖**: 安装构建工具
3. **Node.js依赖**: 安装package.json中的所有包
4. **用户配置**: 创建非root用户
5. **启动脚本**: 配置开发服务器

## 📦 镜像信息

### 构建的镜像
- `locker-template-backend-base:latest` - 后端基础镜像
- `locker-template-frontend-base:latest` - 前端基础镜像

### 镜像大小（预估）
- 后端镜像: ~800MB
- 前端镜像: ~400MB

### 镜像标签策略
- `latest`: 最新版本
- `YYYYMMDD`: 按日期标记的版本

## 🚀 使用基础镜像

### 方式一：使用预构建镜像的docker-compose

```bash
# 使用基础镜像启动项目
docker-compose -f docker-compose.base.yml up -d
```

### 方式二：修改现有docker-compose.yml

将docker-compose.yml中的build配置替换为image配置：

```yaml
# 原配置
backend:
  build:
    context: ./backend
    dockerfile: Dockerfile

# 修改为
backend:
  image: locker-template-backend-base:latest
```

### 方式三：在新项目中使用

在新项目的docker-compose.yml中直接引用基础镜像：

```yaml
services:
  backend:
    image: locker-template-backend-base:latest
    volumes:
      - ./backend:/app
    # 其他配置...
  
  frontend:
    image: locker-template-frontend-base:latest
    volumes:
      - ./frontend:/app
      - /app/node_modules
    # 其他配置...
```

## 🔧 镜像管理

### 查看镜像
```bash
# 查看所有相关镜像
docker images | grep locker-template

# 查看镜像详细信息
docker inspect locker-template-backend-base:latest
```

### 导出镜像（用于离线部署）
```bash
# 导出后端镜像
docker save locker-template-backend-base:latest | gzip > backend-base.tar.gz

# 导出前端镜像
docker save locker-template-frontend-base:latest | gzip > frontend-base.tar.gz
```

### 导入镜像
```bash
# 导入后端镜像
docker load < backend-base.tar.gz

# 导入前端镜像
docker load < frontend-base.tar.gz
```

### 推送到私有仓库
```bash
# 标记镜像
docker tag locker-template-backend-base:latest your-registry.com/locker-template-backend-base:latest

# 推送镜像
docker push your-registry.com/locker-template-backend-base:latest
```

### 清理镜像
```bash
# 删除特定镜像
docker rmi locker-template-backend-base:latest locker-template-frontend-base:latest

# 清理未使用的镜像
docker image prune -f
```

## 🔄 更新基础镜像

### 何时需要更新
- 添加新的Python或Node.js依赖
- 升级现有依赖版本
- 修改系统级配置
- 安全更新

### 更新流程
1. 修改requirements.txt或package.json
2. 重新构建基础镜像
3. 测试新镜像
4. 更新项目中的镜像引用
5. 部署到生产环境

### 版本管理建议
```bash
# 构建带版本标签的镜像
docker build -t locker-template-backend-base:v1.1.0 .
docker build -t locker-template-backend-base:latest .

# 在docker-compose中使用特定版本
image: locker-template-backend-base:v1.1.0
```

## 🛠️ 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 检查Docker状态
docker info

# 清理构建缓存
docker builder prune -f

# 重新构建
docker build --no-cache -t locker-template-backend-base:latest .
```

#### 2. 镜像过大
```bash
# 分析镜像层
docker history locker-template-backend-base:latest

# 使用dive工具分析
dive locker-template-backend-base:latest
```

#### 3. 启动失败
```bash
# 查看容器日志
docker-compose logs backend

# 进入容器调试
docker-compose exec backend bash
```

#### 4. 依赖冲突
```bash
# 检查Python依赖
docker run --rm locker-template-backend-base:latest pip list

# 检查Node.js依赖
docker run --rm locker-template-frontend-base:latest npm list
```

## 📋 最佳实践

### 构建优化
1. **使用.dockerignore**: 排除不必要的文件
2. **多阶段构建**: 减少最终镜像大小
3. **缓存优化**: 合理安排Dockerfile指令顺序
4. **定期更新**: 保持基础镜像和依赖的最新状态

### 安全考虑
1. **非root用户**: 使用专用用户运行应用
2. **最小权限**: 只安装必要的系统包
3. **定期扫描**: 使用工具扫描镜像漏洞
4. **签名验证**: 对生产镜像进行数字签名

### 部署建议
1. **环境隔离**: 为不同环境使用不同的镜像标签
2. **健康检查**: 配置适当的健康检查
3. **资源限制**: 设置内存和CPU限制
4. **日志管理**: 配置日志收集和轮转

## 📚 相关文档

- [Docker官方文档](https://docs.docker.com/)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [项目部署指南](./DEPLOYMENT.md)
- [开发环境搭建](./DEVELOPMENT.md)
