"""
Audit serializers for 快速部署测试
Provides serialization for audit logging and monitoring
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from drf_spectacular.utils import extend_schema_field
from .models import OperationLog, ItemAccessLog, SecurityEvent

User = get_user_model()


class OperationLogSerializer(serializers.ModelSerializer):
    """
    Operation log serializer
    
    Serializes system operation logs with detailed user action information.
    
    Fields:
    - id: Unique log identifier
    - user: User ID who performed the operation
    - user_name: Full name of the user
    - user_email: Email of the user
    - action_type: Type of operation (create, update, delete, etc.)
    - action_display: Display name of the action type
    - result: Operation result (success/failure)
    - target_type: Type of target object
    - target_id: ID of target object
    - target_name: Name of target object
    - description: Operation description
    - extra_data: Additional data (JSON format)
    - ip_address: IP address of the operator
    - user_agent: User agent string
    - request_method: HTTP request method
    - request_path: Request path
    - created_at: Creation time (ISO 8601 format)
    """

    user_name = serializers.CharField(
        source="user.get_full_name", read_only=True, help_text="Full name of the user"
    )
    user_email = serializers.CharField(
        source="user.email", read_only=True, help_text="Email address of the user"
    )
    action_display = serializers.CharField(
        source="get_action_type_display", read_only=True, help_text="Display name of action type"
    )

    class Meta:
        model = OperationLog
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "action_type",
            "action_display",
            "result",
            "target_type",
            "target_id",
            "target_name",
            "description",
            "extra_data",
            "ip_address",
            "user_agent",
            "request_method",
            "request_path",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class ItemAccessLogSerializer(serializers.ModelSerializer):
    """Item access log serializer"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    user_email = serializers.CharField(source="user.email", read_only=True)
    access_type_display = serializers.CharField(
        source="get_access_type_display", read_only=True
    )

    class Meta:
        model = ItemAccessLog
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "content_type",
            "object_id",
            "object_name",
            "access_type",
            "access_type_display",
            "ip_address",
            "user_agent",
            "access_source",
            "source_id",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class SecurityEventSerializer(serializers.ModelSerializer):
    """Security event serializer"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    user_email = serializers.CharField(source="user.email", read_only=True)
    event_type_display = serializers.CharField(
        source="get_event_type_display", read_only=True
    )
    severity_display = serializers.CharField(
        source="get_severity_display", read_only=True
    )
    assigned_to_name = serializers.CharField(
        source="assigned_to.get_full_name", read_only=True
    )

    class Meta:
        model = SecurityEvent
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "event_type",
            "event_type_display",
            "severity",
            "severity_display",
            "status",
            "title",
            "description",
            "event_data",
            "affected_resources",
            "assigned_to",
            "assigned_to_name",
            "resolution_notes",
            "resolved_at",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class AuditStatsSerializer(serializers.Serializer):
    """Audit statistics serializer"""

    total_operations = serializers.IntegerField(read_only=True)
    total_accesses = serializers.IntegerField(read_only=True)
    total_security_events = serializers.IntegerField(read_only=True)
    unresolved_security_events = serializers.IntegerField(read_only=True)

    # Time-based statistics
    operations_by_hour = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )
    operations_by_day = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # User-based statistics
    top_active_users = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # Action type statistics
    operations_by_action = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # Resource type statistics
    operations_by_resource = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )

    # Security event statistics
    security_events_by_type = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )
    security_events_by_severity = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )


class UserActivitySerializer(serializers.Serializer):
    """User activity serializer"""

    user_id = serializers.IntegerField()
    user_name = serializers.CharField()
    user_email = serializers.CharField()

    # Operation statistics
    total_operations = serializers.IntegerField()
    last_operation_time = serializers.DateTimeField()

    # Access statistics
    total_accesses = serializers.IntegerField()
    last_access_time = serializers.DateTimeField()

    # Security event statistics
    security_events_count = serializers.IntegerField()
    last_security_event_time = serializers.DateTimeField(allow_null=True)

    # Recent activities
    recent_operations = OperationLogSerializer(many=True, read_only=True)
    recent_accesses = ItemAccessLogSerializer(many=True, read_only=True)


class AuditSearchSerializer(serializers.Serializer):
    """Audit search serializer"""

    log_type = serializers.ChoiceField(
        choices=[
            ("operation", "Operation Log"),
            ("access", "Access Log"),
            ("security", "Security Event"),
        ],
        required=False,
        help_text="Log type",
    )

    user_id = serializers.IntegerField(required=False, help_text="User ID")

    action = serializers.CharField(required=False, max_length=50, help_text="Action type")

    resource_type = serializers.CharField(
        required=False, max_length=50, help_text="Resource type"
    )

    resource_id = serializers.IntegerField(required=False, help_text="Resource ID")

    start_time = serializers.DateTimeField(required=False, help_text="Start time")

    end_time = serializers.DateTimeField(required=False, help_text="End time")

    ip_address = serializers.IPAddressField(required=False, help_text="IP address")

    keyword = serializers.CharField(
        required=False, max_length=100, help_text="Keyword search"
    )

    def validate(self, attrs):
        """Validate search parameters"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("Start time must be earlier than end time")

        return attrs


class SecurityEventCreateSerializer(serializers.ModelSerializer):
    """Security event creation serializer"""

    class Meta:
        model = SecurityEvent
        fields = [
            "user",
            "event_type",
            "severity",
            "title",
            "description",
            "event_data",
            "affected_resources",
        ]

    def validate_severity(self, value):
        """Validate severity level"""
        if value not in ["low", "medium", "high", "critical"]:
            raise serializers.ValidationError("Invalid severity level")
        return value


class SecurityEventResolveSerializer(serializers.Serializer):
    """Security event resolution serializer"""

    resolution_notes = serializers.CharField(required=False, help_text="Resolution notes")

    def update(self, instance, validated_data):
        """Mark security event as resolved"""
        instance.status = "resolved"
        instance.resolved_at = timezone.now()
        instance.assigned_to = self.context["request"].user

        # Add resolution notes
        if validated_data.get("resolution_notes"):
            instance.resolution_notes = validated_data["resolution_notes"]

        instance.save()
        return instance


class AuditExportSerializer(serializers.Serializer):
    """Audit export serializer"""

    log_type = serializers.ChoiceField(
        choices=[
            ("operation", "Operation Log"),
            ("access", "Access Log"),
            ("security", "Security Event"),
        ],
        help_text="Log type",
    )

    format = serializers.ChoiceField(
        choices=[("csv", "CSV"), ("excel", "Excel"), ("json", "JSON")],
        default="csv",
        help_text="Export format",
    )

    start_time = serializers.DateTimeField(required=False, help_text="Start time")

    end_time = serializers.DateTimeField(required=False, help_text="End time")

    user_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, help_text="User ID list"
    )

    include_details = serializers.BooleanField(
        default=True, help_text="Include detailed information"
    )

    def validate(self, attrs):
        """Validate export parameters"""
        start_time = attrs.get("start_time")
        end_time = attrs.get("end_time")

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("Start time must be earlier than end time")

        return attrs
