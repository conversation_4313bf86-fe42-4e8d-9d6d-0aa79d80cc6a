version: '3.8'

# 使用预构建基础镜像的Docker Compose配置
# 适用于内网环境，无需重新构建镜像

services:
  # 数据库服务
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-kuaisutest_db}
      MYSQL_USER: ${MYSQL_USER:-kuaisutest_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-kuaisutest_password}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root_password_123}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3307:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis服务（用于缓存和会话）
  redis:
    image: redis:7.4
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 后端服务 - 使用预构建的基础镜像
  backend:
    image: locker-template-backend-base:v3
    entrypoint: ""
    command: >
      sh -c "
        python manage.py migrate &&
        python manage.py collectstatic --noinput &&
        python manage.py runserver 0.0.0.0:8000
      "
    volumes:
      - backend_media:/app/media
      - backend_static:/app/static
      - backend_logs:/app/logs
    ports:
      - "8001:8000"
    environment:
      - DEBUG=${DEBUG:-True}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=mysql://${MYSQL_USER:-kuaisutest_user}:${MYSQL_PASSWORD:-kuaisutest_password}@db:3306/${MYSQL_DATABASE:-kuaisutest_db}
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,backend}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:5173,http://127.0.0.1:5173}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 前端服务 - 使用预构建的基础镜像
  frontend:
    image: locker-template-frontend-base:latest
    # volumes:
    #   - ./frontend:/app
    #   - /app/node_modules
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8001}
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=5173
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  backend_media:
  backend_static:
  backend_logs:

networks:
  default:
    name: kuaisutest_network
