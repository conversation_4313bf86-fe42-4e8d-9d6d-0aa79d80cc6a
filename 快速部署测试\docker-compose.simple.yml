version: '3.8'

# 简化的Docker Compose配置 - 仅测试后端基础镜像
# 使用SQLite数据库，无需外部依赖

services:
  # 后端服务 - 使用预构建的基础镜像
  backend:
    image: locker-template-backend-base:latest
    volumes:
      - ./backend:/app
    ports:
      - "8001:8000"
    environment:
      - DEBUG=True
      - SECRET_KEY=test-secret-key-for-demo
      - DATABASE_URL=sqlite:///db.sqlite3
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
      - CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173
    command: >
      sh -c "
        python manage.py migrate &&
        python manage.py init_system_settings &&
        python manage.py shell -c \"
        from django.contrib.auth import get_user_model;
        User = get_user_model();
        User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else print('超级用户已存在')
        \" &&
        python manage.py runserver 0.0.0.0:8000
      "
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
