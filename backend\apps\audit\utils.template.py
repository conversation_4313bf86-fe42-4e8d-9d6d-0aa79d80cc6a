"""
Audit utilities for {{PROJECT_NAME}}
Helper functions for audit logging and security monitoring
"""

from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
import re
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


def get_client_ip(request):
    """Get client IP address from request"""
    if not request:
        return '127.0.0.1'
        
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    
    return ip


def get_user_agent(request):
    """Get user agent from request"""
    if not request:
        return ''
    
    return request.META.get('HTTP_USER_AGENT', '')


def detect_suspicious_activity(user, ip_address):
    """Detect suspicious login activity"""
    try:
        from .models import LoginLog
        
        # Check for login from new IP address
        recent_logins = LoginLog.objects.filter(
            user=user,
            result='success',
            created_at__gte=timezone.now() - timedelta(days=30)
        ).values_list('ip_address', flat=True).distinct()
        
        if ip_address not in recent_logins and recent_logins.count() > 0:
            return True
            
        # Check for rapid login attempts
        recent_attempts = LoginLog.objects.filter(
            user=user,
            created_at__gte=timezone.now() - timedelta(minutes=5)
        ).count()
        
        if recent_attempts > 3:
            return True
            
        return False
        
    except Exception as e:
        logger.error(f"Error detecting suspicious activity: {e}")
        return False


def is_internal_ip(ip_address):
    """Check if IP address is internal/private"""
    internal_patterns = [
        r'^127\.',           # *********/8
        r'^10\.',            # 10.0.0.0/8
        r'^172\.(1[6-9]|2[0-9]|3[01])\.',  # **********/12
        r'^192\.168\.',      # ***********/16
        r'^::1$',            # IPv6 localhost
        r'^fc00:',           # IPv6 unique local
        r'^fe80:',           # IPv6 link local
    ]
    
    for pattern in internal_patterns:
        if re.match(pattern, ip_address):
            return True
    
    return False


def get_location_from_ip(ip_address):
    """Get approximate location from IP address (placeholder)"""
    # This is a placeholder function. In a real implementation,
    # you would integrate with a GeoIP service like MaxMind or IPinfo
    
    if is_internal_ip(ip_address):
        return "Internal Network"
    
    # For external IPs, you would call a GeoIP service
    # Example with a hypothetical service:
    # try:
    #     response = requests.get(f"https://ipapi.co/{ip_address}/json/")
    #     data = response.json()
    #     return f"{data.get('city', 'Unknown')}, {data.get('country_name', 'Unknown')}"
    # except:
    #     return "Unknown Location"
    
    return "External Network"


def parse_user_agent(user_agent):
    """Parse user agent string to extract device information"""
    # This is a simplified parser. For production use,
    # consider using a library like user-agents or ua-parser
    
    device_info = {
        'browser': 'Unknown',
        'os': 'Unknown',
        'device': 'Unknown',
        'is_mobile': False,
        'is_bot': False,
    }
    
    if not user_agent:
        return device_info
    
    user_agent_lower = user_agent.lower()
    
    # Detect bots
    bot_patterns = ['bot', 'crawler', 'spider', 'scraper']
    device_info['is_bot'] = any(pattern in user_agent_lower for pattern in bot_patterns)
    
    # Detect mobile
    mobile_patterns = ['mobile', 'android', 'iphone', 'ipad', 'tablet']
    device_info['is_mobile'] = any(pattern in user_agent_lower for pattern in mobile_patterns)
    
    # Detect browser
    if 'chrome' in user_agent_lower:
        device_info['browser'] = 'Chrome'
    elif 'firefox' in user_agent_lower:
        device_info['browser'] = 'Firefox'
    elif 'safari' in user_agent_lower:
        device_info['browser'] = 'Safari'
    elif 'edge' in user_agent_lower:
        device_info['browser'] = 'Edge'
    
    # Detect OS
    if 'windows' in user_agent_lower:
        device_info['os'] = 'Windows'
    elif 'mac' in user_agent_lower:
        device_info['os'] = 'macOS'
    elif 'linux' in user_agent_lower:
        device_info['os'] = 'Linux'
    elif 'android' in user_agent_lower:
        device_info['os'] = 'Android'
    elif 'ios' in user_agent_lower:
        device_info['os'] = 'iOS'
    
    return device_info


def create_security_event(event_type, severity, title, description, user=None, 
                         affected_resources=None, event_data=None):
    """Create a security event"""
    try:
        from .models import SecurityEvent
        
        SecurityEvent.objects.create(
            event_type=event_type,
            severity=severity,
            title=title,
            description=description,
            user=user,
            affected_resources=affected_resources or [],
            event_data=event_data or {},
        )
        
        logger.warning(f"Security event created: {title}")
        
    except Exception as e:
        logger.error(f"Error creating security event: {e}")


def log_data_access(user, content_type, object_id, object_name, access_type, 
                   request=None, access_source='direct', source_id=None):
    """Log data access for audit trail"""
    try:
        from .models import ItemAccessLog
        
        ip_address = get_client_ip(request) if request else '127.0.0.1'
        user_agent = get_user_agent(request) if request else ''
        
        ItemAccessLog.objects.create(
            user=user,
            content_type=content_type,
            object_id=str(object_id),
            object_name=object_name,
            access_type=access_type,
            ip_address=ip_address,
            user_agent=user_agent,
            access_source=access_source,
            source_id=source_id,
        )
        
    except Exception as e:
        logger.error(f"Error logging data access: {e}")


def check_bulk_operation_threshold(user, action_type, count, time_window_minutes=5):
    """Check if bulk operation exceeds threshold and create security event if needed"""
    try:
        from .models import OperationLog
        
        # Define thresholds for different actions
        thresholds = {
            'item_delete': 10,
            'item_export': 50,
            'user_create': 5,
            'user_delete': 3,
        }
        
        threshold = thresholds.get(action_type, 20)  # Default threshold
        
        if count >= threshold:
            # Check recent operations
            recent_ops = OperationLog.objects.filter(
                user=user,
                action_type=action_type,
                created_at__gte=timezone.now() - timedelta(minutes=time_window_minutes)
            ).count()
            
            if recent_ops >= threshold:
                create_security_event(
                    event_type='bulk_operation',
                    severity='medium',
                    title=f"Bulk {action_type} operation detected",
                    description=f"User {user.username} performed {recent_ops} {action_type} operations in {time_window_minutes} minutes",
                    user=user,
                    event_data={
                        'action_type': action_type,
                        'operation_count': recent_ops,
                        'time_window_minutes': time_window_minutes,
                        'threshold': threshold,
                    }
                )
                
    except Exception as e:
        logger.error(f"Error checking bulk operation threshold: {e}")


def anonymize_sensitive_data(data, fields_to_anonymize=None):
    """Anonymize sensitive data for logging"""
    if not isinstance(data, dict):
        return data
    
    if fields_to_anonymize is None:
        fields_to_anonymize = [
            'password', 'secret', 'token', 'key', 'credential',
            'ssn', 'social_security', 'credit_card', 'card_number'
        ]
    
    anonymized_data = data.copy()
    
    for key, value in data.items():
        key_lower = key.lower()
        if any(sensitive_field in key_lower for sensitive_field in fields_to_anonymize):
            if isinstance(value, str) and len(value) > 4:
                # Show only first 2 and last 2 characters
                anonymized_data[key] = f"{value[:2]}***{value[-2:]}"
            else:
                anonymized_data[key] = "***"
    
    return anonymized_data


class AuditMiddleware:
    """Middleware to add audit context to requests"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Add audit context to request
        request.audit_context = {
            'ip_address': get_client_ip(request),
            'user_agent': get_user_agent(request),
            'timestamp': timezone.now(),
        }
        
        response = self.get_response(request)
        return response
