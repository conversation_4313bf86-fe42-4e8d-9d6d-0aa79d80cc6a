@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🏗️ 开始构建项目基础镜像...

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

REM 项目信息
set PROJECT_NAME=locker-template
set BACKEND_IMAGE=%PROJECT_NAME%-backend-base
set FRONTEND_IMAGE=%PROJECT_NAME%-frontend-base
set VERSION=latest

echo 📦 项目信息:
echo   项目名称: %PROJECT_NAME%
echo   后端镜像: %BACKEND_IMAGE%:%VERSION%
echo   前端镜像: %FRONTEND_IMAGE%:%VERSION%
echo.

REM 构建后端镜像
echo 🔨 构建后端基础镜像...
cd backend
docker build ^
    --tag %BACKEND_IMAGE%:%VERSION% ^
    --build-arg BUILDKIT_INLINE_CACHE=1 ^
    --progress=plain ^
    .

if errorlevel 1 (
    echo ❌ 后端镜像构建失败
    pause
    exit /b 1
) else (
    echo ✅ 后端镜像构建成功: %BACKEND_IMAGE%:%VERSION%
)

cd ..

REM 构建前端镜像
echo 🔨 构建前端基础镜像...
cd frontend
docker build ^
    --tag %FRONTEND_IMAGE%:%VERSION% ^
    --build-arg BUILDKIT_INLINE_CACHE=1 ^
    --progress=plain ^
    .

if errorlevel 1 (
    echo ❌ 前端镜像构建失败
    pause
    exit /b 1
) else (
    echo ✅ 前端镜像构建成功: %FRONTEND_IMAGE%:%VERSION%
)

cd ..

REM 显示构建的镜像
echo.
echo 📋 构建完成的镜像:
docker images | findstr %PROJECT_NAME%

REM 显示镜像大小
echo.
echo 📊 镜像大小信息:
echo 后端镜像大小:
docker images %BACKEND_IMAGE%:%VERSION% --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
echo 前端镜像大小:
docker images %FRONTEND_IMAGE%:%VERSION% --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

echo.
echo 🎉 所有镜像构建完成！
echo.
echo 💡 使用方法:
echo   1. 在项目模板中使用这些基础镜像
echo   2. 修改 docker-compose.yml 中的镜像名称
echo   3. 运行 docker-compose up 启动项目
echo.
echo 🔧 镜像管理命令:
echo   查看镜像: docker images ^| findstr %PROJECT_NAME%
echo   删除镜像: docker rmi %BACKEND_IMAGE%:%VERSION% %FRONTEND_IMAGE%:%VERSION%
echo   推送镜像: docker push %BACKEND_IMAGE%:%VERSION%
echo   推送镜像: docker push %FRONTEND_IMAGE%:%VERSION%
echo.
pause
