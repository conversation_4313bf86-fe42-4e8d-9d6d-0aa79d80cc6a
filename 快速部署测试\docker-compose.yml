version: '3.8'

services:
  # 数据库服务
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: 快速部署测试_db
      POSTGRES_USER: 快速部署测试_user
      POSTGRES_PASSWORD: 快速部署测试_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U 快速部署测试_user -d 快速部署测试_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis服务（用于缓存和会话）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app
      - backend_media:/app/media
      - backend_static:/app/static
      - backend_logs:/app/logs
    ports:
      - "8001:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=postgresql://快速部署测试_user:快速部署测试_password@db:5432/快速部署测试_db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
      - CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "manage.py", "check"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:8001
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=5173
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  backend_media:
  backend_static:
  backend_logs:

networks:
  default:
    name: 快速部署测试_network
