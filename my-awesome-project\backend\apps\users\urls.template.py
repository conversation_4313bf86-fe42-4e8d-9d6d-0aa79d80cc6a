"""
User URLs for My Awesome Project
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

app_name = 'users'

urlpatterns = [
    # Authentication
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('refresh/', views.refresh_token, name='refresh_token'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('profile/settings/', views.UserProfileSettingsView.as_view(), name='user_profile_settings'),
    path('current/', views.current_user, name='current_user'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    
    # User management (admin only)
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
]
