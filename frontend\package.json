{"name": "locker-template-frontend", "version": "1.0.0", "description": "Locker Template - Frontend Application", "private": true, "type": "module", "keywords": ["vue", "vue3", "typescript", "vite", "ant-design-vue", "locker-template"], "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit && vite build --mode production", "build:dev": "vite build --mode development", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:style": "stylelint **/*.{vue,css,scss,less} --fix", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.2.0", "typescript": "^5.1.0", "vite": "^4.3.0", "vue-tsc": "^1.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}