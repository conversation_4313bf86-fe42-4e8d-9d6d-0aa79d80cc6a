{"project": {"name": "My Awesome Project", "title": "My Awesome Project", "description": "A Django + Vue.js application: My Awesome Project", "version": "1.0.0", "slug": "my-awesome-project", "homepage": "", "repository": "", "license": "MIT", "domain": "localhost"}, "author": {"name": "Developer", "email": "<EMAIL>", "url": ""}, "backend": {"framework": "Django", "version": "5.0+", "database": {"engine": "sqlite", "name": "my-awesome-project.sqlite3"}, "host": "localhost", "port": 8000, "debug": true, "secret_key": "Be0KO1ErvftjxiHKtJuR2egrMOweA1dQsv9BcEZhYS_VlCD8t4LhlkDnb5_Jyeakac8"}, "frontend": {"framework": "Vue.js", "version": "3.5+", "ui_library": "Ant Design Vue", "host": "localhost", "port": 5173, "api_base_url": "http://localhost:8000"}, "features": {"core": {"authentication": true, "authorization": true, "user_management": true, "api_documentation": true, "admin_interface": true}, "optional": {"email_notifications": false, "sms_notifications": false, "audit_logging": true, "rate_limiting": false, "caching": false, "background_tasks": false, "file_upload": true, "export_functionality": true, "internationalization": false, "pwa_support": false, "two_factor_auth": false}, "development": {"debug_toolbar": true, "cors": true, "dev_tools": true, "mock_data": false}}, "theme": {"primary_color": "#1890ff", "layout": "side", "dark_mode": false, "compact_mode": false}, "security": {"allowed_hosts": ["localhost", "127.0.0.1"], "cors_allowed_origins": ["http://localhost:5173", "http://localhost:3000"], "jwt_access_token_lifetime": 60, "jwt_refresh_token_lifetime": 7, "enable_https": false}, "database": {"sqlite": {"name": "my-awesome-project.sqlite3"}, "mysql": {"host": "localhost", "port": 3306, "name": "my-awesome-project_db", "user": "root", "password": "password"}, "postgresql": {"host": "localhost", "port": 5432, "name": "my-awesome-project_db", "user": "root", "password": "password"}}, "email": {"backend": "django.core.mail.backends.console.EmailBackend", "host": "smtp.gmail.com", "port": 587, "use_tls": true, "user": "", "password": "", "from_email": "noreply@localhost"}, "redis": {"url": "redis://localhost:6379/1", "cache_ttl": 300}, "celery": {"broker_url": "redis://localhost:6379/0", "result_backend": "redis://localhost:6379/0"}, "logging": {"level": "INFO", "file": "logs/app.log"}, "file_upload": {"max_size": "10MB", "allowed_types": ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]}, "localization": {"default_locale": "en", "fallback_locale": "en", "available_locales": ["en", "zh-CN"]}, "third_party": {"google_analytics_id": "", "sentry_dsn": ""}}